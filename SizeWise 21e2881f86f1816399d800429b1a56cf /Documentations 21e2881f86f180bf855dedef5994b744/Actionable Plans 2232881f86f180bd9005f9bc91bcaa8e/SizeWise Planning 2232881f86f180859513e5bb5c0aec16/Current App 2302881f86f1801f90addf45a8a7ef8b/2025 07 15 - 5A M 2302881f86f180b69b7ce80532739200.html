<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>2025.07.15 - 5A.M.</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="2302881f-86f1-80b6-9b7c-e80532739200" class="page sans"><header><div class="page-header-icon undefined"><span class="icon">📄</span></div><h1 class="page-title">2025.07.15 - 5A.M.</h1><p class="page-description"></p></header><div class="page-body"><h1 id="2302881f-86f1-8075-9c0c-cf52ea1ed236" class="">🎯 <strong>SizeWise Suite - Pixel-Perfect Enterprise Addendum</strong></h1><h2 id="2302881f-86f1-806d-b33b-c22d89a86fbd" class=""><strong>Design System Specification</strong></h2><h3 id="2302881f-86f1-8064-8c09-ee415aa4cf21" class=""><strong>🎨 Color Palette &amp; Tokens</strong></h3><h3 id="2302881f-86f1-804d-adee-e50d48b29d27" class=""><strong>Primary Colors</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8002-ab2b-e94c609a39c4" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Neutral Scale */
--neutral-50:  #fafafa    /* Background light */
--neutral-100: #f5f5f5    /* Background subtle */
--neutral-200: #e5e5e5    /* Border light */
--neutral-300: #d4d4d4    /* Border default */
--neutral-400: #a3a3a3    /* Text muted */
--neutral-500: #737373    /* Text secondary */
--neutral-600: #525252    /* Text primary light */
--neutral-700: #404040    /* Border dark */
--neutral-800: #262626    /* Background dark */
--neutral-900: #171717    /* Background darkest */
--neutral-950: #0a0a0a    /* Background black */

/* Brand Colors */
--blue-50:  #eff6ff
--blue-100: #dbeafe
--blue-400: #60a5fa
--blue-500: #3b82f6      /* Primary brand */
--blue-600: #2563eb      /* Primary hover */
--blue-700: #1d4ed8      /* Primary active */
--blue-900: #1e3a8a      /* Primary dark mode */

/* Semantic Colors */
--green-500: #10b981     /* Success */
--green-600: #059669     /* Success hover */
--red-500:   #ef4444     /* Error */
--red-600:   #dc2626     /* Error hover */
--yellow-500: #f59e0b    /* Warning */
--orange-500: #f97316    /* Info */
</code></pre><h3 id="2302881f-86f1-8049-a1b7-dcb55c941ed5" class=""><strong>Typography Scale</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8020-be2c-faade139e7b2" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Font Family */
--font-family: Inter, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, sans-serif;

/* Font Sizes */
--text-xs:   0.75rem    /* 12px */
--text-sm:   0.875rem   /* 14px */
--text-base: 1rem       /* 16px */
--text-lg:   1.125rem   /* 18px */
--text-xl:   1.25rem    /* 20px */
--text-2xl:  1.5rem     /* 24px */
--text-3xl:  1.875rem   /* 30px */

/* Font Weights */
--font-normal:    400
--font-medium:    500
--font-semibold:  600
--font-bold:      700

/* Line Heights */
--leading-tight:  1.25
--leading-normal: 1.5
--leading-relaxed: 1.625
</code></pre><h3 id="2302881f-86f1-802d-8bcb-c758b97a7b72" class=""><strong>Spacing System (8px Grid)</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80b9-9e80-dcff9db52d60" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">--spacing-0:  0px
--spacing-1:  0.25rem   /* 4px */
--spacing-2:  0.5rem    /* 8px */
--spacing-3:  0.75rem   /* 12px */
--spacing-4:  1rem      /* 16px */
--spacing-5:  1.25rem   /* 20px */
--spacing-6:  1.5rem    /* 24px */
--spacing-8:  2rem      /* 32px */
--spacing-10: 2.5rem    /* 40px */
--spacing-12: 3rem      /* 48px */
--spacing-16: 4rem      /* 64px */
</code></pre><h3 id="2302881f-86f1-8010-9bf2-c1279a560ebd" class=""><strong>Border Radius</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8076-bd11-d5f0c208efc9" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">--radius-none: 0px
--radius-sm:   0.125rem  /* 2px */
--radius:      0.25rem   /* 4px */
--radius-md:   0.375rem  /* 6px */
--radius-lg:   0.5rem    /* 8px */
--radius-xl:   0.75rem   /* 12px */
--radius-2xl:  1rem      /* 16px */
--radius-full: 9999px
</code></pre><h3 id="2302881f-86f1-80f3-8d64-c45c1b9f3bc2" class=""><strong>Shadows &amp; Elevation</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80b9-bce4-f7dbabac1f0b" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">--shadow-sm:  0 1px 2px 0 rgb(0 0 0 / 0.05)
--shadow:     0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)
--shadow-md:  0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)
--shadow-lg:  0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)
--shadow-xl:  0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)
</code></pre><hr id="2302881f-86f1-8088-b176-faf81f31a157"/><h2 id="2302881f-86f1-80d3-aff2-e9a32b3192c1" class=""><strong>📐 Component Specifications</strong></h2><h3 id="2302881f-86f1-8061-acb2-cb5546488e1a" class=""><strong>1. App Shell Layout</strong></h3><h3 id="2302881f-86f1-80b5-8e33-e2d961e8ee27" class=""><strong>Dimensions</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-803d-a3e4-f2f0ac25d9b0" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Header */
height: 64px (4rem)
padding: 0 24px (1.5rem)
z-index: 40
background: #171717 (neutral-900)
border-bottom: 1px solid #262626 (neutral-800)

/* Sidebar */
width-expanded: 256px (16rem)
width-collapsed: 64px (4rem)
background: #171717 (neutral-900)
border-right: 1px solid #262626 (neutral-800)
transition: width 300ms ease-in-out

/* Main Content */
background: #ffffff (white) / #171717 (dark)
padding: 24px (1.5rem)
overflow: auto
</code></pre><h3 id="2302881f-86f1-8091-888e-fea2acbb3393" class=""><strong>Breakpoints</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80b3-9026-e071f066a1c1" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Mobile First */
mobile:  0px - 767px    (sidebar auto-collapsed)
tablet:  768px - 1023px (sidebar collapsible)
desktop: 1024px+        (sidebar expanded by default)
</code></pre><h3 id="2302881f-86f1-807e-b01d-f1df3c0fd3d9" class=""><strong>2. Navigation Components</strong></h3><h3 id="2302881f-86f1-80ca-8bd8-c59428ef33a6" class=""><strong>Sidebar Navigation Items</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8022-b6f4-ca945725cc7d" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Container */
padding: 8px (0.5rem)
gap: 4px (0.25rem)

/* Navigation Item */
padding: 12px (0.75rem)
border-radius: 6px (0.375rem)
font-size: 14px (0.875rem)
font-weight: 500
transition: all 200ms ease

/* Icon */
width: 20px (1.25rem)
height: 20px (1.25rem)
margin-right: 12px (0.75rem) [when expanded]

/* Badge */
min-width: 20px (1.25rem)
height: 20px (1.25rem)
padding: 2px 6px (0.125rem 0.375rem)
border-radius: 9999px (full)
font-size: 12px (0.75rem)
font-weight: 700
</code></pre><h3 id="2302881f-86f1-809c-9d59-c6185ffaab2b" class=""><strong>States</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80dc-baf2-f03394bd91e8" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Default */
color: #a3a3a3 (neutral-400)
background: transparent

/* Hover */
color: #ffffff (white)
background: #262626 (neutral-800)

/* Active */
color: #ffffff (white)
background: #262626 (neutral-800)

/* Focus */
outline: 2px solid #3b82f6 (blue-500)
outline-offset: 2px
</code></pre><h3 id="2302881f-86f1-808b-80b5-c45b6e3c1069" class=""><strong>3. Interactive Components</strong></h3><h3 id="2302881f-86f1-801f-bc08-df5e0d0cf9fd" class=""><strong>Chat Panel</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-806b-b16d-e8b4d1310f76" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Container */
position: fixed
bottom: 16px (1rem)
right: 16px (1rem)
width: 384px (24rem)
height: 540px (33.75rem) [expanded]
height: 64px (4rem) [minimized]
background: #ffffff (white) / #262626 (dark)
border: 1px solid #e5e5e5 (neutral-200) / #404040 (neutral-700)
border-radius: 8px (0.5rem)
box-shadow: var(--shadow-xl)
z-index: 50

/* Header */
padding: 16px (1rem)
border-bottom: 1px solid #e5e5e5 (neutral-200)

/* Tab Navigation */
height: 48px (3rem)
border-bottom: 1px solid #e5e5e5 (neutral-200)

/* Tab Item */
padding: 12px 16px (0.75rem 1rem)
font-size: 14px (0.875rem)
font-weight: 500
border-bottom: 2px solid transparent [default]
border-bottom: 2px solid #3b82f6 (blue-500) [active]

/* Message Area */
height: 320px (20rem)
padding: 16px (1rem)
overflow-y: auto

/* Input Area */
padding: 16px (1rem)
border-top: 1px solid #e5e5e5 (neutral-200)
</code></pre><h3 id="2302881f-86f1-809d-b00c-efca088610df" class=""><strong>Chat Button (Floating)</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80a3-b5aa-dd67801de194" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Container */
position: fixed
bottom: 24px (1.5rem)
right: 24px (1.5rem)
width: 56px (3.5rem)
height: 56px (3.5rem)
background: #3b82f6 (blue-600)
border-radius: 9999px (full)
box-shadow: var(--shadow-lg)
z-index: 40

/* Icon */
width: 24px (1.5rem)
height: 24px (1.5rem)
color: #ffffff (white)

/* Badge */
position: absolute
top: -8px (-0.5rem)
right: -8px (-0.5rem)
min-width: 24px (1.5rem)
height: 24px (1.5rem)
background: #ef4444 (red-500)
color: #ffffff (white)
font-size: 12px (0.75rem)
font-weight: 700
border-radius: 9999px (full)
</code></pre><h3 id="2302881f-86f1-800e-b2a8-eef1dc2953da" class=""><strong>4. Form Components</strong></h3><h3 id="2302881f-86f1-80de-a7e5-fd8497ff1971" class=""><strong>Input Fields</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8015-b362-d251a39c3452" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Container */
width: 100%
padding: 12px 16px (0.75rem 1rem)
border: 1px solid #d4d4d4 (neutral-300)
border-radius: 6px (0.375rem)
font-size: 14px (0.875rem)
background: #ffffff (white) / #262626 (dark)
transition: border-color 200ms ease

/* Focus State */
border-color: #3b82f6 (blue-500)
outline: 2px solid #3b82f6 (blue-500)
outline-offset: 2px

/* Error State */
border-color: #ef4444 (red-500)

/* Disabled State */
background: #f5f5f5 (neutral-100)
color: #a3a3a3 (neutral-400)
cursor: not-allowed
</code></pre><h3 id="2302881f-86f1-8074-a7fe-c3e269c740d1" class=""><strong>Buttons</strong></h3><h3 id="2302881f-86f1-80d3-8cdc-c216146241ca" class=""><strong>Primary Button</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80f6-ba1c-e855f83f0bfb" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">padding: 12px 24px (0.75rem 1.5rem)
background: #3b82f6 (blue-600)
color: #ffffff (white)
border: none
border-radius: 6px (0.375rem)
font-size: 14px (0.875rem)
font-weight: 500
cursor: pointer
transition: background-color 200ms ease

/* Hover */
background: #2563eb (blue-700)

/* Active */
background: #1d4ed8 (blue-800)

/* Disabled */
background: #d4d4d4 (neutral-300)
color: #a3a3a3 (neutral-400)
cursor: not-allowed
</code></pre><h3 id="2302881f-86f1-8029-943f-d0c085ece8b6" class=""><strong>Secondary Button</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8064-9e13-f0b83cd98ee6" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">padding: 12px 24px (0.75rem 1.5rem)
background: transparent
color: #525252 (neutral-600)
border: 1px solid #d4d4d4 (neutral-300)
border-radius: 6px (0.375rem)
font-size: 14px (0.875rem)
font-weight: 500
cursor: pointer
transition: all 200ms ease

/* Hover */
background: #f5f5f5 (neutral-100)
border-color: #a3a3a3 (neutral-400)
</code></pre><h3 id="2302881f-86f1-80e7-8761-f9acb276b94a" class=""><strong>5. Card Components</strong></h3><h3 id="2302881f-86f1-8057-a166-e84aaf11134d" class=""><strong>Standard Card</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-805f-aaaa-e0d65807d9d4" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Container */
background: #ffffff (white) / #262626 (dark)
border: 1px solid #e5e5e5 (neutral-200) / #404040 (neutral-700)
border-radius: 8px (0.5rem)
padding: 24px (1.5rem)
box-shadow: var(--shadow-sm)
transition: box-shadow 200ms ease

/* Hover State */
box-shadow: var(--shadow-md)
transform: translateY(-1px)

/* Header */
margin-bottom: 16px (1rem)
font-size: 18px (1.125rem)
font-weight: 600
color: #171717 (neutral-900) / #ffffff (white)

/* Content */
font-size: 14px (0.875rem)
line-height: 1.5
color: #525252 (neutral-600) / #a3a3a3 (neutral-400)
</code></pre><hr id="2302881f-86f1-80d8-bdb9-ff0950ed9902"/><h2 id="2302881f-86f1-8027-a296-c87420ac2bd9" class=""><strong>📱 Responsive Specifications</strong></h2><h3 id="2302881f-86f1-8053-bf54-d545ef6cb3c2" class=""><strong>Mobile (320px - 767px)</strong></h3><h3 id="2302881f-86f1-807b-a8d0-e7db0febb635" class=""><strong>Layout Adjustments</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80b9-8c93-d0016a04ae8f" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Header */
padding: 0 16px (1rem)
height: 64px (4rem) [unchanged]

/* Sidebar */
position: fixed
top: 64px (4rem)
left: 0
width: 256px (16rem)
height: calc(100vh - 64px)
transform: translateX(-100%) [hidden]
transform: translateX(0) [visible]
z-index: 30

/* Overlay */
position: fixed
inset: 0
background: rgba(0, 0, 0, 0.5)
z-index: 25

/* Main Content */
padding: 16px (1rem)

/* Touch Targets */
min-height: 44px (2.75rem)
min-width: 44px (2.75rem)
</code></pre><h3 id="2302881f-86f1-808e-8458-f951073faff3" class=""><strong>Typography Adjustments</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8040-a633-d871844e39c0" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Headings */
h1: 24px (1.5rem) → 20px (1.25rem)
h2: 20px (1.25rem) → 18px (1.125rem)
h3: 18px (1.125rem) → 16px (1rem)

/* Body Text */
font-size: 16px (1rem) [minimum for readability]
line-height: 1.5
</code></pre><h3 id="2302881f-86f1-807d-85ac-e29dcf033467" class=""><strong>Tablet (768px - 1023px)</strong></h3><h3 id="2302881f-86f1-80d2-9353-f5e4b452c63f" class=""><strong>Layout Adjustments</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80d3-a33f-f56d33fe3adf" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Sidebar */
width: 64px (4rem) [collapsed by default]
width: 256px (16rem) [when expanded]

/* Main Content */
padding: 20px (1.25rem)

/* Grid Layouts */
grid-template-columns: repeat(2, 1fr) [instead of 3 or 4]
gap: 16px (1rem)
</code></pre><hr id="2302881f-86f1-8076-a247-d64a0b4ffc0c"/><h2 id="2302881f-86f1-80e9-8293-f324df959772" class=""><strong>♿ Accessibility Specifications</strong></h2><h3 id="2302881f-86f1-808d-82b0-e610e6a83c77" class=""><strong>Color Contrast Requirements</strong></h3><h3 id="2302881f-86f1-80e0-ab14-e9268fb20aed" class=""><strong>WCAG 2.1 AA Compliance</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8025-a4ca-e2b0df90c903" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Text Contrast Ratios */
Normal text (14px+):     4.5:1 minimum
Large text (18px+):      3:1 minimum
UI components:           3:1 minimum

/* Verified Combinations */
#171717 on #ffffff:      16.75:1 ✅
#525252 on #ffffff:      7.37:1  ✅
#a3a3a3 on #ffffff:      3.11:1  ✅
#ffffff on #3b82f6:      4.78:1  ✅
#ffffff on #171717:      16.75:1 ✅
</code></pre><h3 id="2302881f-86f1-808d-b89b-f164e0bcf5f0" class=""><strong>Focus Indicators</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80a6-942d-c9d38e33bd31" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Focus Ring */
outline: 2px solid #3b82f6 (blue-500)
outline-offset: 2px
border-radius: inherit

/* High Contrast Mode */
@media (prefers-contrast: high) {
  outline-color: ButtonText
  outline-width: 3px
}
</code></pre><h3 id="2302881f-86f1-801b-90dc-ffed46ca097b" class=""><strong>Screen Reader Support</strong></h3><h3 id="2302881f-86f1-80ac-9e7e-c037b558dc1b" class=""><strong>ARIA Labels &amp; Roles</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8067-a90c-ce5f51746a10" class="code"><code class="language-HTML" style="white-space:pre-wrap;word-break:break-all">&lt;!-- Navigation --&gt;
&lt;nav role=&quot;navigation&quot; aria-label=&quot;Main navigation&quot;&gt;
&lt;button aria-expanded=&quot;false&quot; aria-controls=&quot;sidebar&quot;&gt;

&lt;!-- Skip Links --&gt;
&lt;a href=&quot;#main-content&quot; class=&quot;sr-only focus:not-sr-only&quot;&gt;
  Skip to main content
&lt;/a&gt;

&lt;!-- Form Labels --&gt;
&lt;label for=&quot;project-name&quot;&gt;Project Name&lt;/label&gt;
&lt;input id=&quot;project-name&quot; aria-describedby=&quot;name-help&quot;&gt;
&lt;div id=&quot;name-help&quot;&gt;Enter a descriptive project name&lt;/div&gt;

&lt;!-- Status Updates --&gt;
&lt;div aria-live=&quot;polite&quot; aria-atomic=&quot;true&quot;&gt;
  Calculation completed successfully
&lt;/div&gt;
</code></pre><h3 id="2302881f-86f1-8020-9b2b-fed19433163b" class=""><strong>Keyboard Navigation</strong></h3><h3 id="2302881f-86f1-8057-8a00-c87a981afa3d" class=""><strong>Tab Order &amp; Shortcuts</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-801f-a613-da2640350b28" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* Tab Order */
1. Skip link
2. Mobile menu button (if visible)
3. Logo/home link
4. Breadcrumb links
5. User menu button
6. Sidebar navigation items
7. Main content interactive elements

/* Keyboard Shortcuts */
Ctrl/Cmd + B:     Toggle sidebar
F1:               Open help
Escape:           Close modals/dropdowns
Tab:              Next focusable element
Shift + Tab:      Previous focusable element
Enter/Space:      Activate buttons/links
Arrow keys:       Navigate within menus
</code></pre><hr id="2302881f-86f1-8092-9b07-e8a793d653fe"/><h2 id="2302881f-86f1-805d-b665-d59849334010" class=""><strong>⚡ Performance Specifications</strong></h2><h3 id="2302881f-86f1-80be-9bd7-e2e6ab1f789a" class=""><strong>Core Web Vitals Targets</strong></h3><h3 id="2302881f-86f1-80b8-8d17-f163522cf184" class=""><strong>Loading Performance</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8085-90cc-e9c66c5dcd1e" class="code"><code class="language-JavaScript" style="white-space:pre-wrap;word-break:break-all">// Lighthouse Targets
Performance Score:        &gt; 90
First Contentful Paint:   &lt; 1.8s
Largest Contentful Paint: &lt; 2.5s
Speed Index:              &lt; 3.4s
Time to Interactive:      &lt; 3.8s
Total Blocking Time:      &lt; 300ms
Cumulative Layout Shift:  &lt; 0.1
</code></pre><h3 id="2302881f-86f1-80fd-a111-c459b1c2bf72" class=""><strong>Bundle Size Limits</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80b2-8039-ff50e322255f" class="code"><code class="language-JavaScript" style="white-space:pre-wrap;word-break:break-all">// JavaScript Bundles
Initial bundle:           &lt; 200KB gzipped
Route chunks:            &lt; 50KB gzipped each
Total JavaScript:        &lt; 500KB gzipped

// CSS
Critical CSS:            &lt; 20KB
Total CSS:               &lt; 100KB gzipped

// Images
Hero images:             &lt; 100KB (WebP)
Icons:                   SVG preferred
Thumbnails:              &lt; 20KB each
</code></pre><h3 id="2302881f-86f1-8054-ad65-fd0e5d80a69f" class=""><strong>Animation Performance</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80eb-8558-e2e5ea00e7f5" class="code"><code class="language-CSS" style="white-space:pre-wrap;word-break:break-all">/* 60fps Target */
transform: translateX() /* GPU accelerated */
opacity: 0 to 1        /* GPU accelerated */
will-change: transform  /* Hint to browser */

/* Avoid */
width/height animations
box-shadow animations
filter animations (except on GPU)
</code></pre><hr id="2302881f-86f1-8052-9c6f-fe5345fd5b72"/><h2 id="2302881f-86f1-80bc-8a96-ed1d20b30f77" class=""><strong>🧪 Component Acceptance Matrix</strong></h2><h3 id="2302881f-86f1-80a0-a548-c7e8130bcb80" class=""><strong>Sidebar Navigation</strong></h3><table id="2302881f-86f1-8001-8a63-c01b0fa9951a" class="simple-table"><thead class="simple-table-header"><tr id="2302881f-86f1-805e-983c-cac846295824"><th id="H=Bc" class="simple-table-header-color simple-table-header">Test Case</th><th id="tMBz" class="simple-table-header-color simple-table-header">Mobile (320px)</th><th id="f:kh" class="simple-table-header-color simple-table-header">Tablet (768px)</th><th id="lsM`" class="simple-table-header-color simple-table-header">Desktop (1024px+)</th><th id="Si@D" class="simple-table-header-color simple-table-header">Acceptance Criteria</th></tr></thead><tbody><tr id="2302881f-86f1-80bb-bc23-d122cc5fedfd"><td id="H=Bc" class=""><strong>Initial State</strong></td><td id="tMBz" class="">Collapsed, hidden</td><td id="f:kh" class="">Collapsed, visible</td><td id="lsM`" class="">Expanded, visible</td><td id="Si@D" class="">✅ Correct width per breakpoint</td></tr><tr id="2302881f-86f1-806f-9d18-fe5e97012de5"><td id="H=Bc" class=""><strong>Toggle Action</strong></td><td id="tMBz" class="">Slide in/out</td><td id="f:kh" class="">Expand/collapse</td><td id="lsM`" class="">Expand/collapse</td><td id="Si@D" class="">✅ Smooth 300ms transition</td></tr><tr id="2302881f-86f1-8042-8900-e6afc57e86e8"><td id="H=Bc" class=""><strong>Touch Targets</strong></td><td id="tMBz" class="">44px minimum</td><td id="f:kh" class="">44px minimum</td><td id="lsM`" class="">44px minimum</td><td id="Si@D" class="">✅ WCAG compliance</td></tr><tr id="2302881f-86f1-801c-9ee1-fee9543c6724"><td id="H=Bc" class=""><strong>Keyboard Nav</strong></td><td id="tMBz" class="">Tab order logical</td><td id="f:kh" class="">Tab order logical</td><td id="lsM`" class="">Tab order logical</td><td id="Si@D" class="">✅ Focus visible, Escape closes</td></tr><tr id="2302881f-86f1-80ca-9018-ff9bff32dad5"><td id="H=Bc" class=""><strong>Screen Reader</strong></td><td id="tMBz" class="">Proper ARIA labels</td><td id="f:kh" class="">Proper ARIA labels</td><td id="lsM`" class="">Proper ARIA labels</td><td id="Si@D" class="">✅ Navigation announced correctly</td></tr></tbody></table><h3 id="2302881f-86f1-80c1-881b-c97925b492df" class=""><strong>Chat Panel</strong></h3><table id="2302881f-86f1-802c-a8e4-ebbf75da5802" class="simple-table"><thead class="simple-table-header"><tr id="2302881f-86f1-80f5-8739-fb55db228363"><th id="TaeC" class="simple-table-header-color simple-table-header">Test Case</th><th id="k&lt;in" class="simple-table-header-color simple-table-header">Mobile (320px)</th><th id="mBuW" class="simple-table-header-color simple-table-header">Tablet (768px)</th><th id="IFKG" class="simple-table-header-color simple-table-header">Desktop (1024px+)</th><th id="`&gt;pS" class="simple-table-header-color simple-table-header">Acceptance Criteria</th></tr></thead><tbody><tr id="2302881f-86f1-804b-94b8-ecf22eb12a52"><td id="TaeC" class=""><strong>Panel Size</strong></td><td id="k&lt;in" class="">90% viewport width</td><td id="mBuW" class="">384px fixed</td><td id="IFKG" class="">384px fixed</td><td id="`&gt;pS" class="">✅ Responsive sizing</td></tr><tr id="2302881f-86f1-803c-b471-ff7c77bb609a"><td id="TaeC" class=""><strong>Position</strong></td><td id="k&lt;in" class="">Bottom center</td><td id="mBuW" class="">Bottom right</td><td id="IFKG" class="">Bottom right</td><td id="`&gt;pS" class="">✅ Proper positioning</td></tr><tr id="2302881f-86f1-80c7-aac5-db07155fd1f0"><td id="TaeC" class=""><strong>Tab Switching</strong></td><td id="k&lt;in" class="">Touch friendly</td><td id="mBuW" class="">Touch/click</td><td id="IFKG" class="">Click</td><td id="`&gt;pS" class="">✅ Active state visible</td></tr><tr id="2302881f-86f1-8025-9392-d22c072d0357"><td id="TaeC" class=""><strong>Message Input</strong></td><td id="k&lt;in" class="">Virtual keyboard</td><td id="mBuW" class="">Physical keyboard</td><td id="IFKG" class="">Physical keyboard</td><td id="`&gt;pS" class="">✅ Enter sends message</td></tr><tr id="2302881f-86f1-8004-8c45-e31c821efedc"><td id="TaeC" class=""><strong>Minimize State</strong></td><td id="k&lt;in" class="">64px height</td><td id="mBuW" class="">64px height</td><td id="IFKG" class="">64px height</td><td id="`&gt;pS" class="">✅ Smooth animation</td></tr></tbody></table><h3 id="2302881f-86f1-802e-8d1c-c4442e7be5b1" class=""><strong>Form Components</strong></h3><table id="2302881f-86f1-8072-abca-fb0eaa77e355" class="simple-table"><thead class="simple-table-header"><tr id="2302881f-86f1-80f6-a1d4-c4fc355dfdd0"><th id="piOZ" class="simple-table-header-color simple-table-header">Component</th><th id="zxqa" class="simple-table-header-color simple-table-header">Focus State</th><th id="LflS" class="simple-table-header-color simple-table-header">Error State</th><th id="yrB{" class="simple-table-header-color simple-table-header">Disabled State</th><th id="uYrj" class="simple-table-header-color simple-table-header">Acceptance Criteria</th></tr></thead><tbody><tr id="2302881f-86f1-80da-b9d8-c806bb3810c9"><td id="piOZ" class=""><strong>Text Input</strong></td><td id="zxqa" class="">Blue border + ring</td><td id="LflS" class="">Red border</td><td id="yrB{" class="">Gray background</td><td id="uYrj" class="">✅ 4.5:1 contrast ratio</td></tr><tr id="2302881f-86f1-80e4-9137-f3353041a061"><td id="piOZ" class=""><strong>Button Primary</strong></td><td id="zxqa" class="">Blue ring</td><td id="LflS" class="">N/A</td><td id="yrB{" class="">Gray + disabled cursor</td><td id="uYrj" class="">✅ 44px touch target</td></tr><tr id="2302881f-86f1-80d1-9110-fbc29a58c511"><td id="piOZ" class=""><strong>Button Secondary</strong></td><td id="zxqa" class="">Blue ring</td><td id="LflS" class="">N/A</td><td id="yrB{" class="">Gray + disabled cursor</td><td id="uYrj" class="">✅ 3:1 border contrast</td></tr><tr id="2302881f-86f1-8069-b1cc-fab727804a4c"><td id="piOZ" class=""><strong>Select Dropdown</strong></td><td id="zxqa" class="">Blue border + ring</td><td id="LflS" class="">Red border</td><td id="yrB{" class="">Gray background</td><td id="uYrj" class="">✅ Arrow key navigation</td></tr></tbody></table><hr id="2302881f-86f1-8079-b067-dfc013d848fc"/><h2 id="2302881f-86f1-80ce-b74c-fa05290c5c13" class=""><strong>🎨 Visual Regression Test Matrix</strong></h2><h3 id="2302881f-86f1-80ec-977c-f93d2642e760" class=""><strong>Component Screenshots Required</strong></h3><h3 id="2302881f-86f1-80d3-a1ba-c37c446caab0" class=""><strong>Light Mode</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8026-a506-f3a9888694da" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">✅ Sidebar - Expanded state
✅ Sidebar - Collapsed state
✅ Header - Full width
✅ Header - Mobile with hamburger
✅ Chat panel - All tabs
✅ Chat panel - Minimized
✅ Modal - Help documentation
✅ Cards - Hover states
✅ Buttons - All variants and states
✅ Forms - Focus and error states
</code></pre><h3 id="2302881f-86f1-80b9-a064-d77d29f27587" class=""><strong>Dark Mode</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8067-8249-ce507f823796" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">✅ All above components in dark theme
✅ Color contrast verification
✅ Focus ring visibility
✅ Border visibility in dark mode
</code></pre><h3 id="2302881f-86f1-80b7-8677-d7729f8547a5" class=""><strong>Responsive Breakpoints</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8005-b58d-d8667bbbeec9" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">✅ 320px (iPhone SE)
✅ 375px (iPhone 12)
✅ 768px (iPad Portrait)
✅ 1024px (iPad Landscape)
✅ 1440px (Desktop)
✅ 1920px (Large Desktop)
</code></pre><hr id="2302881f-86f1-80fd-817e-f3ef4892a83a"/><h2 id="2302881f-86f1-8061-abbf-d0d81b8b9983" class=""><strong>📋 Production Readiness Checklist</strong></h2><h3 id="2302881f-86f1-808e-8f4a-ec8eca68dba2" class=""><strong>Design System Compliance</strong></h3><ul id="2302881f-86f1-8043-b914-d7e61fdfe0ea" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">All components use design tokens</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80eb-9535-c43d967b7e90" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Color contrast meets WCAG 2.1 AA</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-8095-bd47-d3c2b9cc0d67" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Typography scale consistently applied</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-8008-a6c0-f8241b983ce0" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Spacing follows 8px grid system</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-8034-8243-e035cb465473" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Border radius values standardized</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-8046-bd94-c644fba2c0a5" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Shadow/elevation system implemented</span><div class="indented"></div></li></ul><h3 id="2302881f-86f1-8080-979a-e7c4f0619eed" class=""><strong>Responsive Design</strong></h3><ul id="2302881f-86f1-805b-a293-feaa170f6d4d" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Mobile-first CSS approach</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-8082-9ca8-c1d2dac46b9a" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Touch targets minimum 44px</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80b6-95b5-f9311c88c22b" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Breakpoint behavior verified</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-805a-8edf-d18b70529b23" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Horizontal scrolling eliminated</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80fe-bb21-e1c15d2cf2ec" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Text remains readable at all sizes</span><div class="indented"></div></li></ul><h3 id="2302881f-86f1-80c8-954a-f0a86b7a2053" class=""><strong>Accessibility</strong></h3><ul id="2302881f-86f1-8071-a3e4-eb9dc3482bc7" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Semantic HTML structure</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-8098-80a3-f03607f299a5" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">ARIA labels and roles implemented</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-802e-961d-eadd8a39b25e" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Keyboard navigation functional</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80f3-a324-d36c6b3d7aab" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Screen reader testing completed</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-8008-a62a-cdf8b531c010" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Focus management working</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-804a-9403-f6ca2b4a2b2f" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Skip links implemented</span><div class="indented"></div></li></ul><h3 id="2302881f-86f1-8071-bc9a-cdc2901ab7c9" class=""><strong>Performance</strong></h3><ul id="2302881f-86f1-8079-a5fb-d4ef00808f20" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Core Web Vitals targets met</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80aa-9c3a-f8bd187e932a" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Bundle sizes within limits</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-806e-8530-c516dd2b7716" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Images optimized and responsive</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80e5-85b3-f9dddec2f77a" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Animations run at 60fps</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80fb-9def-ec5bab0ec420" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Memory leaks eliminated</span><div class="indented"></div></li></ul><h3 id="2302881f-86f1-80b4-be17-f85d09754a8d" class=""><strong>Cross-Browser Testing</strong></h3><ul id="2302881f-86f1-80a9-86e1-fbe948533c76" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Chrome (latest 2 versions)</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80e8-8422-efd9bea53632" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Firefox (latest 2 versions)</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80f4-a8b5-c8ffd3316964" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Safari (latest 2 versions)</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-8015-b7c9-e0d4b4321a36" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Edge (latest 2 versions)</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80c2-97da-e9a2ac6032e7" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Mobile Safari (iOS)</span><div class="indented"></div></li></ul><ul id="2302881f-86f1-80c7-a2f7-e9f7545eea48" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Chrome Mobile (Android)</span><div class="indented"></div></li></ul><hr id="2302881f-86f1-80fb-850a-ed4c0d76a7f6"/><p id="2302881f-86f1-80e8-92e8-ea556d613851" class="">This pixel-perfect specification provides the granular detail needed for enterprise-grade handoffs, design system documentation, and formal QA processes. Every measurement, color, and interaction is precisely defined to eliminate ambiguity and ensure consistent implementation across teams.</p></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>