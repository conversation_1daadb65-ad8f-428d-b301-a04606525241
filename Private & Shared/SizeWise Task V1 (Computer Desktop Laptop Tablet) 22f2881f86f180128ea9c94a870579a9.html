<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>SizeWise Task V1 (Computer/Desktop/Laptop/Tablet)</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="22f2881f-86f1-8012-8ea9-c94a870579a9" class="page sans"><header><h1 class="page-title">SizeWise Task V1 (Computer/Desktop/Laptop/Tablet)</h1><p class="page-description"></p><table class="properties"><tbody><tr class="property-row property-row-status"><th><span class="icon property-icon"><div data-testid="/icons/burst_gray.svg" style="width:14px;height:14px;flex-shrink:0;transform:scale(1.2);mask:url(/icons/burst_gray.svg?mode=light) no-repeat center;-webkit-mask:url(/icons/burst_gray.svg?mode=light) no-repeat center;background-color:rgba(71, 70, 68, 0.6);fill:rgba(71, 70, 68, 0.6)"></div></span>Status</th><td><span class="status-value"><div class="status-dot"></div>Not started</span></td></tr></tbody></table></header><div class="page-body"><details open=""><summary style="font-weight:600;font-size:1.25em;line-height:1.3;margin:0">Testing</summary><div class="indented"><ul id="2312881f-86f1-8079-b9bc-ea43de999231" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Must have a production ready deployment with admin access;</span><div class="indented"></div></li></ul><ul id="2312881f-86f1-8023-8d4b-f8c4ac77bda7" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Must have a production ready deployment with user access only;</span><div class="indented"></div></li></ul></div></details><details open=""><summary style="font-weight:600;font-size:1.25em;line-height:1.3;margin:0">Global UI</summary><div class="indented"><ul id="2312881f-86f1-80c8-92ec-c209594bb2a9" class="toggle"><li><details open=""><summary>Background</summary><ul id="2312881f-86f1-8001-bbc7-ffa019a32ce4" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Dark Mode - Black</span><div class="indented"></div></li></ul><ul id="2312881f-86f1-80a8-ac91-f402a52bbe81" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Light Mode - White</span><div class="indented"></div></li></ul><ul id="2312881f-86f1-8025-8155-f5070012ac49" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">System - Dark/Light Mode</span><div class="indented"></div></li></ul></details></li></ul><ul id="2302881f-86f1-8059-b49f-fe47f9881e31" class="toggle"><li><details open=""><summary>Navigation Structure</summary><h1 id="2312881f-86f1-8088-b927-fadaa632d754" class=""><strong>SizeWise Suite – Centered Top Navigation (No Sidebar, Minimalist, Desktop-First)</strong></h1><hr id="2312881f-86f1-8043-93f0-ea1a89ace1d0"/><h2 id="2312881f-86f1-8005-b999-fea3da954f85" class=""><strong>UI Designer Notes &amp; Rationale</strong></h2><ul id="2312881f-86f1-809a-9b08-e84d314084b6" class="bulleted-list"><li style="list-style-type:disc"><strong>Centered Top Bar Navigation</strong><ul id="2312881f-86f1-80f1-a4be-d7acc7be736a" class="bulleted-list"><li style="list-style-type:circle">The main navigation bar is centered at the top of the screen and always visible on all desktop/tablet/iPad layouts.</li></ul><ul id="2312881f-86f1-80d0-ad65-e168f2795f6b" class="bulleted-list"><li style="list-style-type:circle">All primary workflow items—including Home, File, Projects, Tools, and Profile—are aligned horizontally, with equal visual weight and dropdown submenus for each.</li></ul><ul id="2312881f-86f1-8027-b2a5-fd076e07fec4" class="bulleted-list"><li style="list-style-type:circle">The <strong>Profile menu</strong> is inline with other top nav items (not isolated at the top-right) and uses a user icon or avatar; its dropdown includes Profile &amp; Account, Settings, Reports &amp; Exports, Administrative Access (if user is admin), and all relevant personal/user settings.</li></ul><ul id="2312881f-86f1-8003-9aef-fa9de3691b5c" class="bulleted-list"><li style="list-style-type:circle">No top-right “island”—all navigation is unified and centered for symmetry and easy scanning.</li></ul></li></ul><ul id="2312881f-86f1-8007-b8df-c9220ec795ef" class="bulleted-list"><li style="list-style-type:disc"><strong>No Sidebar, No Mobile/Responsive Navigation</strong><ul id="2312881f-86f1-807a-9a64-cdd3fffa68fb" class="bulleted-list"><li style="list-style-type:circle">The UI is designed strictly for desktop, laptop, and tablet/iPad; there is no sidebar or bottom/hamburger nav.</li></ul><ul id="2312881f-86f1-80da-a638-e422b940b516" class="bulleted-list"><li style="list-style-type:circle">All navigation, quick access, and context switching are handled through the top bar and its dropdowns/popovers.</li></ul></li></ul><ul id="2312881f-86f1-80f5-87c5-caa9fc02d3e4" class="bulleted-list"><li style="list-style-type:disc"><strong>Bottom-Right Corner (Persistent)</strong><ul id="2312881f-86f1-8009-84ad-d0e382234afe" class="bulleted-list"><li style="list-style-type:circle">A rectangular button for <strong>Chat &amp; Notifications</strong> sits above a round “?” Help &amp; Docs button.</li></ul><ul id="2312881f-86f1-807a-a4f2-f66640a6edb6" class="bulleted-list"><li style="list-style-type:circle"><strong>Chat &amp; Notifications</strong>: Clicking opens a compact modal with tabs for Team Chat, Support, and Notifications (Updates, Alerts); the modal can be expanded/maximized.</li></ul><ul id="2312881f-86f1-8050-83e5-f0c3a7f1137f" class="bulleted-list"><li style="list-style-type:circle"><strong>Help &amp; Docs</strong>: A persistent question-mark button at the bottom right corner opens a pop-up help/docs window, which can also be maximized from the same position.</li></ul></li></ul><ul id="2312881f-86f1-80a7-b1d5-ca3517e987c2" class="bulleted-list"><li style="list-style-type:disc"><strong>Clarity, Discoverability, and Focus</strong><ul id="2312881f-86f1-80f7-b000-d70eeaace432" class="bulleted-list"><li style="list-style-type:circle">No clutter, redundancy, or visual confusion. All major workflows and user/account features are immediately accessible, with the Profile menu inline for clarity.</li></ul><ul id="2312881f-86f1-8007-809e-f18f6f901b47" class="bulleted-list"><li style="list-style-type:circle">Quick actions, recent files, and settings are logically grouped in dropdowns.</li></ul><ul id="2312881f-86f1-8033-a9d9-c6a654d28281" class="bulleted-list"><li style="list-style-type:circle">Support, documentation, and team communication tools are always available—never hidden or multiple clicks away.</li></ul><ul id="2312881f-86f1-80e8-9ae5-e31c9a25e227" class="bulleted-list"><li style="list-style-type:circle">The entire layout is optimized for professionals working on larger screens who expect everything to be quickly accessible and discoverable without mobile-specific patterns.</li></ul></li></ul><hr id="2312881f-86f1-80c7-b20a-cb677bc159a3"/><h2 id="2312881f-86f1-806d-aa8b-db1992b839df" class=""><strong>Navigation Layout Mockup</strong></h2><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-80e6-ba59-e194e843f7b4" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">plaintext
CopyEdit
┌─────────────────────────────────────────────────────────────────────────────┐
│          LOGO     |  Home  |  File  |  Projects  |  Tools  |  Profile  |    │
└─────────────────────────────────────────────────────────────────────────────┘

[Profile Menu Dropdown]
    - Profile &amp; Account
        - Password/Security
        - Connected Accounts
        - API/Integrations
    - Settings
        - Language
        - Units (Imperial/SI)
        - Theme
    - Reports &amp; Exports
        - My Exports (History)
        - Export Formats (PDF, Excel, CAD, etc.)
        - Batch Export
    - Administrative Access (admin only)
        - User Management
        - Team Permissions
        - Audit Logs
        - Company Profile
        - Billing &amp; Subscription
        - Cloud Sync Settings
    - Logout

[Bottom Right Corner]
    [⬛]
    ┌───────────────────┐
    │  🔔 Notifications │
    │  💬 Chat         │
    └───────────────────┘

    [❓]
    ┌───────────────────────────┐
    │  Help &amp; Documentation     │
    │  (with maximize button)   │
    └───────────────────────────┘

</code></pre><h2 id="2312881f-86f1-8040-b24b-f03c937e216a" class=""><strong>Detailed Navigation Content (for Dev/UI Handoff)</strong></h2><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-80b0-8b98-f5f0780833f6" class="code"><code class="language-Markdown" style="white-space:pre-wrap;word-break:break-all">markdown
CopyEdit
- Home
    - Dashboard
        - Start New Project
        - Recently Opened
        - Favorites
        - Quick Access to Tools
        - Onboarding Checklist / Guided Tour
- File
    - New Project
    - Open Project
    - Save Project
    - Import
    - Export
    - Recent Files
    - My Calculations (History)
    - My Exports/Reports
- Projects
    - All Projects
    - Project Templates
    - Create New Project
    - Project Archive
    - Search/Filter Projects
- Tools
    - Air Duct Sizer
    - Combustion Vent Sizer
    - Grease Duct Sizer
    - Generator Exhaust Sizer
    - Estimating App
    - [Room for future tools: Insulation Sizer, Plumbing Calculator, etc.]
- Profile
    - Profile &amp; Account
        - Password/Security
        - Connected Accounts
        - API/Integrations
    - Settings
        - Language
        - Global Units (Imperial/SI)
        - Theme
    - Reports &amp; Exports
        - My Exports (History)
        - Export Formats (PDF, Excel, CAD, etc.)
        - Batch Export
    - Administrative Access (admin only)
        - User Management
        - Team Permissions
        - Audit Logs
        - Company Profile
        - Billing &amp; Subscription
        - Cloud Sync Settings
    - Logout

[Bottom Right Corner]
    - [Rectangular Button: Notifications &amp; Chat]
	    - Opens help/documentation pop-up, with maximize option.
        - 🔔 Notifications (tab)
            - Updates &amp; Release Notes
            - Support Replies
            - Standards/Compliance Alerts
        - 💬 Chat (tab)
            - Team Chat
            - Announcements (admin-broadcast)
            - Support
    - [❓ Help &amp; Docs Button]
	    - Opens help/documentation pop-up, with maximize option.

</code></pre><hr id="2312881f-86f1-809d-a30f-c0a54ab06b5a"/><h2 id="2312881f-86f1-803e-b69d-eff1d99122db" class=""><strong>Summary Rationale for Designer/Dev Handoff</strong></h2><ul id="2312881f-86f1-8067-b9dd-c01043878e72" class="bulleted-list"><li style="list-style-type:disc"><strong>No sidebar</strong>: Reduces cognitive clutter, maximizes workspace, and matches your vision for a desktop-class tool.</li></ul><ul id="2312881f-86f1-8066-a07c-fda918f8cd6b" class="bulleted-list"><li style="list-style-type:disc"><strong>Top bar is always centered</strong>, minimal, and clear.</li></ul><ul id="2312881f-86f1-80fc-b71e-c01f2ec3a84f" class="bulleted-list"><li style="list-style-type:disc"><strong>Profile menu</strong>: All personal, admin, and export/reporting controls unified here.</li></ul><ul id="2312881f-86f1-80c5-a29c-cd0baf0162a3" class="bulleted-list"><li style="list-style-type:disc"><strong>Help/Docs</strong>: Persistent, bottom-right, out of the way but always accessible, pop-up with maximize.</li></ul><ul id="2312881f-86f1-80b2-8778-dd20f6c7b5ea" class="bulleted-list"><li style="list-style-type:disc"><strong>Chat/Notifications</strong>: Grouped above Help, in a compact, easily reached rectangle.</li></ul><ul id="2312881f-86f1-801d-a37b-c865e76cbbd9" class="bulleted-list"><li style="list-style-type:disc"><strong>No command palette or mobile nav</strong> for now.</li></ul><ul id="2312881f-86f1-808d-96f6-f95890154032" class="bulleted-list"><li style="list-style-type:disc"><strong>All admin functions</strong> are discoverable but secured within Profile.</li></ul></details></li></ul><ul id="2312881f-86f1-8088-89fc-f512da596f43" class="toggle"><li><details open=""><summary>Dashboard</summary><h1 id="2312881f-86f1-8045-bda3-f412e19cb0dd" class=""><strong>SizeWise Suite Dashboard – Minimalist Layout (v1, Context-Aware)</strong></h1><hr id="2312881f-86f1-80aa-8eaf-d4a2b6fd887c"/><h2 id="2312881f-86f1-80e2-bf59-eebfd9ca116a" class=""><strong>UI Designer &amp; Developer Rationale</strong></h2><h3 id="2312881f-86f1-808f-9f37-f3bc332d7e8e" class=""><strong>Key Principles</strong></h3><ul id="2312881f-86f1-8071-adae-e94c07b2f371" class="bulleted-list"><li style="list-style-type:disc"><strong>Maximal focus:</strong> Only show controls, actions, and info relevant to the current user context.</li></ul><ul id="2312881f-86f1-802f-84e5-fd7876a868d4" class="bulleted-list"><li style="list-style-type:disc"><strong>Clean workspace:</strong> No persistent messages or help buttons except on the dashboard (never on tool or detail screens).</li></ul><ul id="2312881f-86f1-8056-9a21-eafe38e050f4" class="bulleted-list"><li style="list-style-type:disc"><strong>Modern B2B UX:</strong> Aligns with pro-grade engineering/SaaS standards (Linear, Vercel, Notion).</li></ul><ul id="2312881f-86f1-80b4-a906-c4e3fd66b6e6" class="bulleted-list"><li style="list-style-type:disc"><strong>Scalable &amp; future-proof:</strong> Layout accommodates future expansion (KPI/stats, team modules) without redesign.</li></ul><hr id="2312881f-86f1-8096-86bc-e7cd0ccf48c0"/><h3 id="2312881f-86f1-801e-84af-cfa9b5ed83ff" class=""><strong>1. Top Bar (Centered Navigation)</strong></h3><ul id="2312881f-86f1-8077-901a-d6849b4206d2" class="bulleted-list"><li style="list-style-type:disc">Centered, horizontal navigation with dropdowns:<ul id="2312881f-86f1-806d-9c0b-f7a554818aad" class="bulleted-list"><li style="list-style-type:circle"><strong>Home</strong> (Dashboard)</li></ul><ul id="2312881f-86f1-802b-8d98-f4c3c92df4bc" class="bulleted-list"><li style="list-style-type:circle"><strong>File</strong></li></ul><ul id="2312881f-86f1-80cb-807a-c87aa4322531" class="bulleted-list"><li style="list-style-type:circle"><strong>Projects</strong></li></ul><ul id="2312881f-86f1-80a3-a9de-f538a505e150" class="bulleted-list"><li style="list-style-type:circle"><strong>Tools</strong></li></ul><ul id="2312881f-86f1-8085-8402-c0c1f1e0693a" class="bulleted-list"><li style="list-style-type:circle"><strong>Profile</strong> (with Settings, Reports/Exports, Admin—if user is admin)</li></ul></li></ul><hr id="2312881f-86f1-800c-b119-e7849175b863"/><h3 id="2312881f-86f1-8006-a86c-d4a74ff34529" class=""><strong>2. Dashboard Content Sections</strong></h3><p id="2312881f-86f1-80ef-8e29-d0cab1654f35" class=""><strong>A. Top Section – Quick Actions</strong></p><ul id="2312881f-86f1-8015-89e8-c666304e0edd" class="bulleted-list"><li style="list-style-type:disc">Horizontally aligned buttons:<ul id="2312881f-86f1-80a0-9872-e6d38acd93b2" class="bulleted-list"><li style="list-style-type:circle">Start New Project</li></ul><ul id="2312881f-86f1-80df-ba8e-fed4751f9c20" class="bulleted-list"><li style="list-style-type:circle">Start New Calculation</li></ul><ul id="2312881f-86f1-801a-9953-e5d6a4841e57" class="bulleted-list"><li style="list-style-type:circle">Import Plan</li></ul><ul id="2312881f-86f1-8054-8b60-e96af5a07142" class="bulleted-list"><li style="list-style-type:circle">Export Report</li></ul></li></ul><hr id="2312881f-86f1-8012-a8dc-c1c8fab586cd"/><p id="2312881f-86f1-8097-bbcc-cd1110786da4" class=""><strong>B. Section 1 – Projects &amp; Recent Activity (Combined)</strong></p><ul id="2312881f-86f1-80a7-9dc1-e957217ee642" class="bulleted-list"><li style="list-style-type:disc"><strong>Active Projects</strong><ul id="2312881f-86f1-8010-9a4f-e2fba22f9991" class="bulleted-list"><li style="list-style-type:circle">Card/list view for each active project:<ul id="2312881f-86f1-8082-894c-d1e56e2683b8" class="bulleted-list"><li style="list-style-type:square">Project Name &amp; Icon</li></ul><ul id="2312881f-86f1-8063-857b-df33e78885be" class="bulleted-list"><li style="list-style-type:square">Status (in progress, awaiting, completed)</li></ul><ul id="2312881f-86f1-80ac-96b6-c1c135160be0" class="bulleted-list"><li style="list-style-type:square">Deadline/Last updated</li></ul><ul id="2312881f-86f1-80a5-b86a-c0c42252b61f" class="bulleted-list"><li style="list-style-type:square">Actions: Open, Resume</li></ul></li></ul><ul id="2312881f-86f1-801c-8007-f4f16f570202" class="bulleted-list"><li style="list-style-type:circle"><strong>Recently Opened</strong> (chip or small icon)</li></ul><ul id="2312881f-86f1-809c-addc-ea2f221fdff3" class="bulleted-list"><li style="list-style-type:circle"><strong>Favorites</strong> (optional)</li></ul><ul id="2312881f-86f1-80c8-8c03-d248e06e8e4d" class="bulleted-list"><li style="list-style-type:circle"><strong>Recent Activity</strong><ul id="2312881f-86f1-8030-8a9a-c5d984ac6569" class="bulleted-list"><li style="list-style-type:square">Inline with each project: last calculation, export, or edit with timestamp (“Last export: 1 hour ago”, “Last calculation: 10:34 AM”)</li></ul><ul id="2312881f-86f1-803e-80e5-e6cba20aa406" class="bulleted-list"><li style="list-style-type:square">“Resume” button for incomplete/draft work</li></ul></li></ul></li></ul><hr id="2312881f-86f1-80da-b8e6-cf312f383690"/><p id="2312881f-86f1-80cf-9ee2-d69191a640cf" class=""><strong>C. Section 2 – Tools Shortcuts</strong></p><ul id="************************************" class="bulleted-list"><li style="list-style-type:disc">Icon cards or buttons for each core tool:<ul id="2312881f-86f1-8090-aed6-ee0d0e72a0dc" class="bulleted-list"><li style="list-style-type:circle">Air Duct Sizer</li></ul><ul id="2312881f-86f1-8041-b1c2-f7a39bf82742" class="bulleted-list"><li style="list-style-type:circle">Combustion Vent Sizer</li></ul><ul id="2312881f-86f1-805c-a576-e898c0d5bcbf" class="bulleted-list"><li style="list-style-type:circle">Grease Duct Sizer</li></ul><ul id="2312881f-86f1-803d-95b8-c2f5fddebd8d" class="bulleted-list"><li style="list-style-type:circle">Generator Exhaust Sizer</li></ul><ul id="2312881f-86f1-80d3-bccf-c92cfde7ebd5" class="bulleted-list"><li style="list-style-type:circle">Estimating App</li></ul></li></ul><ul id="2312881f-86f1-8087-b5f2-fc3657fef950" class="bulleted-list"><li style="list-style-type:disc">Optionally: Highlight “Last Used” or most popular tools</li></ul><hr id="2312881f-86f1-80d1-8706-d840ba324080"/><p id="2312881f-86f1-80db-9737-e49c820b7f71" class=""><strong>D. Messages &amp; Help (Context-Aware)</strong></p><ul id="2312881f-86f1-802d-b0aa-c3afeffe6e38" class="bulleted-list"><li style="list-style-type:disc"><strong>Dashboard only:</strong><ul id="2312881f-86f1-8032-b656-d35f6e9f3014" class="bulleted-list"><li style="list-style-type:circle">Bottom right:<ul id="2312881f-86f1-8008-9625-c99c00b5d81b" class="bulleted-list"><li style="list-style-type:square">[💬 Messages] — Rectangular button opens modal with notifications, chat, support</li></ul><ul id="2312881f-86f1-80bb-b7b4-f8306215e767" class="bulleted-list"><li style="list-style-type:square">[❓ Help] — Circular button opens modal with onboarding, FAQ, guided tour, training links</li></ul></li></ul></li></ul><ul id="2312881f-86f1-807b-a497-d119ad9c42cf" class="bulleted-list"><li style="list-style-type:disc"><strong>All other screens:</strong><ul id="2312881f-86f1-807f-8fbd-c8dff631a16b" class="bulleted-list"><li style="list-style-type:circle"><em>Neither button is shown.</em></li></ul><ul id="2312881f-86f1-806f-9b1b-ff44cdc0475f" class="bulleted-list"><li style="list-style-type:circle">Maximum workspace—no floating UI elements</li></ul></li></ul><hr id="2312881f-86f1-80e3-be59-c01b87ef71b1"/><p id="2312881f-86f1-8088-8bbb-c284383db110" class=""><strong>E. Getting Started</strong></p><ul id="2312881f-86f1-802d-913b-c51fa3fa6a29" class="bulleted-list"><li style="list-style-type:disc">Accessed from the Help button (never persistent on dashboard)</li></ul><ul id="2312881f-86f1-80b3-85d6-e5c68c5ffa8a" class="bulleted-list"><li style="list-style-type:disc">Includes onboarding checklist, app tour, training resources</li></ul><hr id="2312881f-86f1-80f9-9bff-f2e685e31095"/><p id="2312881f-86f1-8056-bb96-d5316044180d" class=""><strong>What’s Not Present (by design):</strong></p><ul id="2312881f-86f1-806f-b1d2-da2da845959c" class="bulleted-list"><li style="list-style-type:disc">KPIs/Stats — not on v1 dashboard</li></ul><ul id="2312881f-86f1-80f1-8b30-eb318eb565a7" class="bulleted-list"><li style="list-style-type:disc">Team/Collab Feed — not in v1</li></ul><ul id="2312881f-86f1-8064-846f-dfb43fa238d8" class="bulleted-list"><li style="list-style-type:disc">System/Account Status — found under Profile, not dashboard</li></ul><ul id="2312881f-86f1-80fd-ae0f-d49de0088291" class="bulleted-list"><li style="list-style-type:disc">No floating messages/help except on dashboard</li></ul><hr id="2312881f-86f1-80d6-be77-da434acdd102"/><h2 id="2312881f-86f1-808c-9f0f-fa889e08457b" class=""><strong>Minimalist Dashboard Wireframe</strong></h2><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-8037-80b4-f399ca84b6fb" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">plaintext
CopyEdit
┌────────────────────────────────────────────────────────────────────────────┐
│      Home  |  File  |  Projects  |  Tools  |  Profile   [Centered Top Bar] │
└────────────────────────────────────────────────────────────────────────────┘

[Start New Project]   [Start Calculation]   [Import Plan]   [Export Report]

─────────────────────────────────────────────────────────────

Active Projects &amp; Recent Activity
[Project Card]   [Project Card]   [Project Card]
  - Name        - Status         - Last Activity
  - Actions: Open/Resume/Favorite
  - Recent: Last calc/export/edit (timestamp, inline)

─────────────────────────────────────────────────────────────

Tools Shortcuts
[Air Duct Sizer]   [Combustion Vent Sizer]   [Grease Duct Sizer]
[Generator Exhaust]   [Estimating App]

─────────────────────────────────────────────────────────────

Bottom right (dashboard only):
  [💬] Messages  [❓] Help

*On any other screen (tools, project detail): NO messages/help buttons shown*

</code></pre><hr id="2312881f-86f1-80f0-ab98-f211e5b89c41"/><h2 id="2312881f-86f1-80e0-bd3f-f7f444039267" class=""><strong>Summary Table: Dashboard Elements</strong></h2><table id="2312881f-86f1-8006-a2bd-f17c0e39d533" class="simple-table"><thead class="simple-table-header"><tr id="2312881f-86f1-800b-90fe-f2e00c8a5198"><th id="^;:[" class="simple-table-header-color simple-table-header">Section</th><th id="qSjm" class="simple-table-header-color simple-table-header">Shown On Dashboard</th><th id="D_@W" class="simple-table-header-color simple-table-header">Shown In Tools/Other</th></tr></thead><tbody><tr id="2312881f-86f1-8048-ab96-edfd2a3e18f1"><td id="^;:[" class="">Top Nav Bar</td><td id="qSjm" class="">✔</td><td id="D_@W" class="">✔</td></tr><tr id="2312881f-86f1-80cf-ab99-d0e02b085ef1"><td id="^;:[" class="">Quick Actions</td><td id="qSjm" class="">✔</td><td id="D_@W" class="">✗</td></tr><tr id="2312881f-86f1-802e-93d2-d37ff4be516c"><td id="^;:[" class="">Projects/Recent Activity</td><td id="qSjm" class="">✔</td><td id="D_@W" class="">✗</td></tr><tr id="2312881f-86f1-8099-a613-ee971b8c623e"><td id="^;:[" class="">Tools Shortcuts</td><td id="qSjm" class="">✔</td><td id="D_@W" class="">✗</td></tr><tr id="2312881f-86f1-803b-99f0-e8a5a9d0f068"><td id="^;:[" class="">Messages &amp; Help Buttons</td><td id="qSjm" class="">✔</td><td id="D_@W" class="">✗</td></tr></tbody></table><hr id="2312881f-86f1-80da-b467-f3d8b80a7cb2"/><h2 id="2312881f-86f1-8068-a95a-f72149a106d7" class=""><strong>Key Rationale Points</strong></h2><ul id="2312881f-86f1-8055-96c0-e28bf5a91f14" class="bulleted-list"><li style="list-style-type:disc"><strong>Only relevant actions and info are ever visible.</strong></li></ul><ul id="2312881f-86f1-80bb-a211-ecdbffad9a90" class="bulleted-list"><li style="list-style-type:disc"><strong>Messages/help never distract from actual work; dashboard is the “control center” for notifications and onboarding.</strong></li></ul><ul id="2312881f-86f1-80d0-8276-e2bb35b67d08" class="bulleted-list"><li style="list-style-type:disc"><strong>Cards are visually minimalist, well-spaced, and ready for new sections as you scale.</strong></li></ul><ul id="2312881f-86f1-804c-9965-ca0a63050628" class="bulleted-list"><li style="list-style-type:disc"><strong>All navigation, action, and info is at most one click away, without persistent clutter.</strong></li></ul></details></li></ul><ul id="2312881f-86f1-8017-8f74-c91232f06d8b" class="toggle"><li><details open=""><summary>Navigation Menu </summary><ul id="2312881f-86f1-80a9-823f-ca1abd6c16dd" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Navigation Menu to be at the top of the screen always </span><div class="indented"><ul id="2312881f-86f1-80cf-a655-ebc8ecb69cf3" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Navigation Menu to be at the top of the screen in window mode</span><div class="indented"></div></li></ul><ul id="2312881f-86f1-80a5-aeee-e2e628ee919a" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Navigation Menu hidden in full screen mode</span><div class="indented"></div></li></ul></div></li></ul></details></li></ul><ul id="2312881f-86f1-802e-ac78-eb06612abdc4" class="toggle"><li><details open=""><summary>Effects</summary><div id="2312881f-86f1-80ad-a7ac-e0e7a3378e4b" class="column-list"><div id="2312881f-86f1-80ba-881a-c81218fed1af" style="width:50%" class="column"><ul id="2312881f-86f1-80a7-a284-d381dd81dc7f" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Frame</span><div class="indented"></div></li></ul></div><div id="2312881f-86f1-800c-b85d-de1ce2c4df2b" style="width:50%" class="column"><ul id="2312881f-86f1-805c-9c7f-ff6a1b4cf2d0" class="toggle"><li><details open=""><summary>Code</summary><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-80e0-b62a-ee647e3d2919" class="code"><code class="language-TypeScript"></code></pre></details></li></ul></div></div><div id="2312881f-86f1-80a1-b50e-c9c032610683" class="column-list"><div id="2312881f-86f1-809d-b9af-e3f0aceac92a" style="width:50%" class="column"><ul id="2312881f-86f1-8090-910a-c28d26893fc3" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Navigation Menu</span><div class="indented"></div></li></ul></div><div id="2312881f-86f1-803d-aad6-c2ea6f5b6de9" style="width:50%" class="column"><ul id="2312881f-86f1-80e8-9523-da036935eca0" class="toggle"><li><details open=""><summary>dock-label-at-hover</summary><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-8001-8e9e-d9b2d7064da8" class="code"><code class="language-TypeScript">import * as React from &quot;react&quot;
import { motion } from &quot;framer-motion&quot;
import { cn } from &quot;@/lib/utils&quot;
import { LucideIcon } from &quot;lucide-react&quot;

interface DockProps {
  className?: string
  items: {
    icon: LucideIcon
    label: string
    onClick?: () =&gt; void
  }[]
}

interface DockIconButtonProps {
  icon: LucideIcon
  label: string
  onClick?: () =&gt; void
  className?: string
}

const floatingAnimation = {
  initial: { y: 0 },
  animate: {
    y: [-2, 2, -2],
    transition: {
      duration: 4,
      repeat: Infinity,
      ease: &quot;easeInOut&quot;
    }
  }
}

const DockIconButton = React.forwardRef&lt;HTMLButtonElement, DockIconButtonProps&gt;(
  ({ icon: Icon, label, onClick, className }, ref) =&gt; {
    return (
      &lt;motion.button
        ref={ref}
        whileHover={{ scale: 1.1, y: -2 }}
        whileTap={{ scale: 0.95 }}
        onClick={onClick}
        className={cn(
          &quot;relative group p-3 rounded-lg&quot;,
          &quot;hover:bg-secondary transition-colors&quot;,
          className
        )}
      &gt;
        &lt;Icon className=&quot;w-5 h-5 text-foreground&quot; /&gt;
        &lt;span className={cn(
          &quot;absolute -top-8 left-1/2 -translate-x-1/2&quot;,
          &quot;px-2 py-1 rounded text-xs&quot;,
          &quot;bg-popover text-popover-foreground&quot;,
          &quot;opacity-0 group-hover:opacity-100&quot;,
          &quot;transition-opacity whitespace-nowrap pointer-events-none&quot;
        )}&gt;
          {label}
        &lt;/span&gt;
      &lt;/motion.button&gt;
    )
  }
)
DockIconButton.displayName = &quot;DockIconButton&quot;

const Dock = React.forwardRef&lt;HTMLDivElement, DockProps&gt;(
  ({ items, className }, ref) =&gt; {
    return (
      &lt;div ref={ref} className={cn(&quot;w-full h-64 flex items-center justify-center p-2&quot;, className)}&gt;
        &lt;div className=&quot;w-full max-w-4xl h-64 rounded-2xl flex items-center justify-center relative&quot;&gt;
          &lt;motion.div
            initial=&quot;initial&quot;
            animate=&quot;animate&quot;
            variants={floatingAnimation}
            className={cn(
              &quot;flex items-center gap-1 p-2 rounded-2xl&quot;,
              &quot;backdrop-blur-lg border shadow-lg&quot;,
              &quot;bg-background/90 border-border&quot;,
              &quot;hover:shadow-xl transition-shadow duration-300&quot;
            )}
          &gt;
            {items.map((item) =&gt; (
              &lt;DockIconButton key={item.label} {...item} /&gt;
            ))}
          &lt;/motion.div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    )
  }
)
Dock.displayName = &quot;Dock&quot;

export { Dock }</code></pre></details></li></ul><ul id="2302881f-86f1-80e6-9cd4-d97f4de444dc" class="toggle"><li><details open=""><summary>Messages and Notification pop-up</summary><ul id="2302881f-86f1-808f-9866-eaa4d342667b" class="toggle"><li><details open=""><summary>Toast</summary><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-80de-8983-e7ae16ca643c" class="code"><code class="language-Plain Text">&#x27;use client&#x27;

import { forwardRef, useImperativeHandle, useRef } from &#x27;react&#x27;;
import { motion } from &#x27;framer-motion&#x27;;
import {
  Toaster as SonnerToaster,
  toast as sonnerToast,
} from &#x27;sonner&#x27;;
import {
  CheckCircle,
  AlertCircle,
  Info,
  AlertTriangle,
  X,
} from &#x27;lucide-react&#x27;;

import { Button } from &#x27;@/components/ui/button&#x27;;
import { cn } from &#x27;@/lib/utils&#x27;;

type Variant = &#x27;default&#x27; | &#x27;success&#x27; | &#x27;error&#x27; | &#x27;warning&#x27;;
type Position =
  | &#x27;top-left&#x27;
  | &#x27;top-center&#x27;
  | &#x27;top-right&#x27;
  | &#x27;bottom-left&#x27;
  | &#x27;bottom-center&#x27;
  | &#x27;bottom-right&#x27;;

interface ActionButton {
  label: string;
  onClick: () =&gt; void;
  variant?: &#x27;default&#x27; | &#x27;outline&#x27; | &#x27;ghost&#x27;;
}

interface ToasterProps {
  title?: string;
  message: string;
  variant?: Variant;
  duration?: number;
  position?: Position;
  actions?: ActionButton;
  onDismiss?: () =&gt; void;
  highlightTitle?: boolean;
}

export interface ToasterRef {
  show: (props: ToasterProps) =&gt; void;
}

const variantStyles: Record&lt;Variant, string&gt; = {
  default: &#x27;bg-card border-border text-foreground&#x27;,
  success: &#x27;bg-card border-green-600/50&#x27;,
  error: &#x27;bg-card border-destructive/50&#x27;,
  warning: &#x27;bg-card border-amber-600/50&#x27;,
};

const titleColor: Record&lt;Variant, string&gt; = {
  default: &#x27;text-foreground&#x27;,
  success: &#x27;text-green-600 dark:text-green-400&#x27;,
  error: &#x27;text-destructive&#x27;,
  warning: &#x27;text-amber-600 dark:text-amber-400&#x27;,
};

const iconColor: Record&lt;Variant, string&gt; = {
  default: &#x27;text-muted-foreground&#x27;,
  success: &#x27;text-green-600 dark:text-green-400&#x27;,
  error: &#x27;text-destructive&#x27;,
  warning: &#x27;text-amber-600 dark:text-amber-400&#x27;,
};

const variantIcons: Record&lt;Variant, React.ComponentType&lt;{ className?: string }&gt;&gt; = {
  default: Info,
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
};

const toastAnimation = {
  initial: { opacity: 0, y: 50, scale: 0.95 },
  animate: { opacity: 1, y: 0, scale: 1 },
  exit: { opacity: 0, y: 50, scale: 0.95 },
};

const Toaster = forwardRef&lt;ToasterRef, { defaultPosition?: Position }&gt;(
  ({ defaultPosition = &#x27;bottom-right&#x27; }, ref) =&gt; {
    const toastReference = useRef&lt;ReturnType&lt;typeof sonnerToast.custom&gt; | null&gt;(null);

    useImperativeHandle(ref, () =&gt; ({
      show({
        title,
        message,
        variant = &#x27;default&#x27;,
        duration = 4000,
        position = defaultPosition,
        actions,
        onDismiss,
        highlightTitle,
      }) {
        const Icon = variantIcons[variant];

        toastReference.current = sonnerToast.custom(
          (toastId) =&gt; (
            &lt;motion.div
              variants={toastAnimation}
              initial=&quot;initial&quot;
              animate=&quot;animate&quot;
              exit=&quot;exit&quot;
              transition={{ duration: 0.3, ease: &#x27;easeOut&#x27; }}
              className={cn(
                &#x27;flex items-center justify-between w-full max-w-xs p-3 rounded-xl border shadow-md&#x27;,
                variantStyles[variant]
              )}
            &gt;
              &lt;div className=&quot;flex items-start gap-2&quot;&gt;
                &lt;Icon className={cn(&#x27;h-4 w-4 mt-0.5 flex-shrink-0&#x27;, iconColor[variant])} /&gt;
                &lt;div className=&quot;space-y-0.5&quot;&gt;
                  {title &amp;&amp; (
                    &lt;h3
                      className={cn(
                        &#x27;text-xs font-medium leading-none&#x27;,
                        titleColor[variant],
                        highlightTitle &amp;&amp; titleColor[&#x27;success&#x27;] // override for meeting case
                      )}
                    &gt;
                      {title}
                    &lt;/h3&gt;
                  )}
                  &lt;p className=&quot;text-xs text-muted-foreground&quot;&gt;{message}&lt;/p&gt;
                &lt;/div&gt;
              &lt;/div&gt;

              &lt;div className=&quot;flex items-center gap-2&quot;&gt;
                {actions?.label &amp;&amp; (
                  &lt;Button
                    variant={actions.variant || &#x27;outline&#x27;}
                    size=&quot;sm&quot;
                    onClick={() =&gt; {
                      actions.onClick();
                      sonnerToast.dismiss(toastId);
                    }}
                    className={cn(
                      &#x27;cursor-pointer&#x27;,
                      variant === &#x27;success&#x27;
                        ? &#x27;text-green-600 border-green-600 hover:bg-green-600/10 dark:hover:bg-green-400/20&#x27;
                        : variant === &#x27;error&#x27;
                        ? &#x27;text-destructive border-destructive hover:bg-destructive/10 dark:hover:bg-destructive/20&#x27;
                        : variant === &#x27;warning&#x27;
                        ? &#x27;text-amber-600 border-amber-600 hover:bg-amber-600/10 dark:hover:bg-amber-400/20&#x27;
                        : &#x27;text-foreground border-border hover:bg-muted/10 dark:hover:bg-muted/20&#x27;
                    )}
                  &gt;
                    {actions.label}
                  &lt;/Button&gt;
                )}

                &lt;button
                  onClick={() =&gt; {
                    sonnerToast.dismiss(toastId);
                    onDismiss?.();
                  }}
                  className=&quot;rounded-full p-1 hover:bg-muted/50 dark:hover:bg-muted/30 transition-colors focus:outline-none focus:ring-2 focus:ring-ring&quot;
                  aria-label=&quot;Dismiss notification&quot;
                &gt;
                  &lt;X className=&quot;h-3 w-3 text-muted-foreground&quot; /&gt;
                &lt;/button&gt;
              &lt;/div&gt;
            &lt;/motion.div&gt;
          ),
          { duration, position }
        );
      },
    }));

    return (
      &lt;SonnerToaster
        position={defaultPosition}
        toastOptions={{ unstyled: true, className: &#x27;flex justify-end&#x27; }}
      /&gt;
    );
  }
);

export default Toaster;
</code></pre></details></li></ul><ul id="2302881f-86f1-80e2-903d-d227d59fb4da" class="toggle"><li><details open=""><summary>button</summary><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-8003-8a70-f41ca563c165" class="code"><code class="language-Plain Text">import * as React from &quot;react&quot;
import { Slot } from &quot;@radix-ui/react-slot&quot;
import { cva, type VariantProps } from &quot;class-variance-authority&quot;

import { cn } from &quot;@/lib/utils&quot;

const buttonVariants = cva(
  &quot;inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50&quot;,
  {
    variants: {
      variant: {
        default: &quot;bg-primary text-primary-foreground hover:bg-primary/90&quot;,
        destructive:
          &quot;bg-destructive text-destructive-foreground hover:bg-destructive/90&quot;,
        outline:
          &quot;border border-input bg-background hover:bg-accent hover:text-accent-foreground&quot;,
        secondary:
          &quot;bg-secondary text-secondary-foreground hover:bg-secondary/80&quot;,
        ghost: &quot;hover:bg-accent hover:text-accent-foreground&quot;,
        link: &quot;text-primary underline-offset-4 hover:underline&quot;,
      },
      size: {
        default: &quot;h-10 px-4 py-2&quot;,
        sm: &quot;h-9 rounded-md px-3&quot;,
        lg: &quot;h-11 rounded-md px-8&quot;,
        icon: &quot;h-10 w-10&quot;,
      },
    },
    defaultVariants: {
      variant: &quot;default&quot;,
      size: &quot;default&quot;,
    },
  },
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes&lt;HTMLButtonElement&gt;,
    VariantProps&lt;typeof buttonVariants&gt; {
  asChild?: boolean
}

const Button = React.forwardRef&lt;HTMLButtonElement, ButtonProps&gt;(
  ({ className, variant, size, asChild = false, ...props }, ref) =&gt; {
    const Comp = asChild ? Slot : &quot;button&quot;
    return (
      &lt;Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      /&gt;
    )
  },
)
Button.displayName = &quot;Button&quot;

export { Button, buttonVariants }
</code></pre></details></li></ul><ul id="2302881f-86f1-80da-b992-c6800b9fa018" class="toggle"><li><details open=""><summary>Task for Augment</summary><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2302881f-86f1-800c-b955-db20f2bf16f0" class="code"><code class="language-Plain Text">Absolutely! Here’s a step-by-step Augment implementation protocol (including integration code samples) so this animated Toaster notification effect will be used for all warnings, errors, and info toasts throughout your app and tools.

⸻

🚦 Task for Augment: Apply Custom Toaster Effect for All Warnings, Info, Errors in SizeWise Suite

⸻

1. Centralize the Toaster

a. Place the Toaster component once at the root of your app shell (usually in /app/layout.tsx), so that all pages and components can trigger toasts.

// /app/layout.tsx
import Toaster, { ToasterRef } from &#x27;@/components/ui/Toaster&#x27;;
import React, { useRef } from &#x27;react&#x27;;

export default function RootLayout({ children }) {
  const toasterRef = useRef&lt;ToasterRef&gt;(null);

  // You may pass this ref via context/provider to children if needed

  return (
    &lt;html lang=&quot;en&quot;&gt;
      &lt;body&gt;
        {/* ...AppShell/sidebar/header... */}
        {children}
        &lt;Toaster ref={toasterRef} defaultPosition=&quot;bottom-right&quot; /&gt;
      &lt;/body&gt;
    &lt;/html&gt;
  );
}


⸻

2. Make the Toaster Globally Triggerable

a. Expose a global toast utility, e.g. via React Context or a custom hook.
b. Example: useToaster hook (placed in /lib/hooks/useToaster.ts):

// /lib/hooks/useToaster.ts
import React, { useContext, createContext, useRef } from &quot;react&quot;;
import Toaster, { ToasterRef } from &quot;@/components/ui/Toaster&quot;;

const ToasterContext = createContext&lt;React.RefObject&lt;ToasterRef&gt; | null&gt;(null);

export function ToasterProvider({ children }: { children: React.ReactNode }) {
  const toasterRef = useRef&lt;ToasterRef&gt;(null);
  return (
    &lt;ToasterContext.Provider value={toasterRef}&gt;
      {children}
      &lt;Toaster ref={toasterRef} defaultPosition=&quot;bottom-right&quot; /&gt;
    &lt;/ToasterContext.Provider&gt;
  );
}

export function useToaster() {
  const ctx = useContext(ToasterContext);
  if (!ctx) throw new Error(&quot;useToaster must be within ToasterProvider&quot;);
  return ctx;
}

c. Then, wrap your RootLayout or AppShell with &lt;ToasterProvider&gt;:

// /app/layout.tsx or /app/AppShell.tsx
import { ToasterProvider } from &quot;@/lib/hooks/useToaster&quot;;

export default function RootLayout({ children }) {
  return (
    &lt;html lang=&quot;en&quot;&gt;
      &lt;body&gt;
        &lt;ToasterProvider&gt;
          {/* ...rest of app... */}
          {children}
        &lt;/ToasterProvider&gt;
      &lt;/body&gt;
    &lt;/html&gt;
  );
}


⸻

3. Trigger Toaster Notifications Anywhere

a. In any component (tool, validation, warning, etc):

import { useToaster } from &quot;@/lib/hooks/useToaster&quot;;

export default function MyComponent() {
  const toasterRef = useToaster();

  function handleError() {
    toasterRef.current?.show({
      variant: &quot;error&quot;,
      title: &quot;Calculation Error&quot;,
      message: &quot;Input is invalid or exceeds SMACNA limits.&quot;,
      actions: {
        label: &quot;Fix Input&quot;,
        onClick: () =&gt; {
          // focus or scroll to input field
        },
      },
    });
  }

  return (
    &lt;button onClick={handleError}&gt;
      Trigger Error Toast
    &lt;/button&gt;
  );
}

	•	Use &quot;error&quot;, &quot;warning&quot;, &quot;success&quot;, or &quot;default&quot; as needed.
	•	You can use this for warnings, validation errors, user notifications, export success, etc.

⸻

4. Apply in All Tools &amp; App-wide

a. Replace all usages of window.alert, console.warn, or existing basic toasts/snackbars with the new Toaster effect.
	•	In tools (e.g., Air Duct Sizer), trigger a warning when a value exceeds standards.
	•	In project screens, notify on successful save/export.
	•	In admin, use for permission errors or confirmations.

b. Example for validation inside a tool:

if (input &gt; MAX_VALUE) {
  toasterRef.current?.show({
    variant: &quot;warning&quot;,
    title: &quot;Value Too High&quot;,
    message: &quot;The value entered exceeds the allowed maximum for this tool.&quot;,
    highlightTitle: true,
  });
}

c. For async events or API errors:

try {
  await api.saveProject(data);
  toasterRef.current?.show({
    variant: &quot;success&quot;,
    title: &quot;Saved&quot;,
    message: &quot;Your project was saved successfully!&quot;,
  });
} catch (e) {
  toasterRef.current?.show({
    variant: &quot;error&quot;,
    title: &quot;Save Failed&quot;,
    message: &quot;There was a problem saving your project.&quot;,
  });
}


⸻

5. Design Consistency
	•	Always use the Toaster for warnings, validation errors, status alerts, and user feedback in all core tools and app sections.
	•	Ensure that only one or two toasts are visible at a time for clarity.
	•	Adjust variant, actions, and position as appropriate for the UX context.

⸻

Summary for Augment

Implement the custom Toaster effect as the universal notification system:
	•	Place &lt;Toaster /&gt; at the root layout, using context if needed for global access.
	•	Provide a hook (useToaster) so any component can trigger notifications.
	•	Refactor all warnings, errors, and info messages in all tools/app screens to use this effect instead of browser alerts or legacy toasts.
	•	Reference and build upon the code samples above for consistency and accessibility.
	•	Test in all themes and on mobile/desktop.

⸻

If any tool or page still uses old alert methods, update to this new system.
If you need code or integration help for a specific tool, request clarification.

⸻

Let me know if you want an admin-only override, custom themes, or a more advanced API!</code></pre></details></li></ul></details></li></ul><ul id="2312881f-86f1-8072-92cb-f42daeb42fcc" class="toggle"><li><details open=""><summary>Augment Implementation protocol</summary><p id="2312881f-86f1-8088-8dd8-cd8597fefa17" class="">Here’s an <strong>instruction set for Augment</strong> that is clear, precise, and ready for team implementation.</p><p id="2312881f-86f1-806f-aa71-e1474c40badd" class="">This covers both <strong>navigation menu hover effect</strong> and <strong>universal animated Toaster notifications</strong>—with explicit expectations and reference code.</p><hr id="2312881f-86f1-80ca-8958-e4b7287c126c"/><h2 id="2312881f-86f1-807b-b8c1-c1f5ff0cc990" class="">🚦 Augment Implementation Protocol</h2><h3 id="2312881f-86f1-80a2-9bee-f3df4477a389" class="">1. <strong>Navigation Menu – “Dock Label at Hover” Effect</strong></h3><p id="2312881f-86f1-8057-8b10-e0ee1172586d" class=""><strong>Requirement:</strong></p><p id="2312881f-86f1-803f-b7de-d6fcef952efb" class="">All items in the top navigation menu must use the <code>dock-label-at-hover</code> effect (animated label reveal on hover, floating animation for icons).</p><p id="2312881f-86f1-8095-a33d-f9466c3f5963" class=""><strong>Integration Steps:</strong></p><ul id="2312881f-86f1-80d4-9c74-c847bd85f650" class="bulleted-list"><li style="list-style-type:disc"><strong>Use the provided Dock component for all nav icons/menus:</strong><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-809a-81a8-ce8bb99237f8" class="code"><code class="language-TypeScript" style="white-space:pre-wrap;word-break:break-all">import { Dock } from &quot;@/components/ui/Dock&quot;;
// Sample usage:
&lt;Dock
  items={[
    { icon: Home, label: &quot;Home&quot;, onClick: ... },
    { icon: Folder, label: &quot;File&quot;, onClick: ... },
    // ...other nav items
  ]}
/&gt;
</code></pre></li></ul><ul id="2312881f-86f1-80a8-af37-e977b687b24e" class="bulleted-list"><li style="list-style-type:disc"><strong>Effect:</strong><ul id="2312881f-86f1-8083-9dce-c2b7e6269097" class="bulleted-list"><li style="list-style-type:circle">Icon floats gently (framer-motion animation)</li></ul><ul id="2312881f-86f1-806a-8ff6-eecb5a605860" class="bulleted-list"><li style="list-style-type:circle">Label fades in above icon on hover</li></ul><ul id="2312881f-86f1-800f-86fc-c4520f72e580" class="bulleted-list"><li style="list-style-type:circle">Applies to all main nav items (Home, File, Projects, Tools, Profile, etc.)</li></ul></li></ul><ul id="2312881f-86f1-801d-955a-dc8039d57ceb" class="bulleted-list"><li style="list-style-type:disc"><strong>Where:</strong><ul id="2312881f-86f1-80a3-9568-cd537d2e2a61" class="bulleted-list"><li style="list-style-type:circle">Top navigation bar, centered horizontally, all dropdowns and menu groups.</li></ul></li></ul><hr id="2312881f-86f1-80f1-9973-f04314e33566"/><h3 id="2312881f-86f1-80e3-b659-f8f14f6b2231" class="">2. <strong>Messages and Notification Pop-up – Universal Animated Toaster</strong></h3><p id="2312881f-86f1-8033-a3d8-f8935f927acb" class=""><strong>Requirement:</strong></p><p id="2312881f-86f1-8074-92df-c0109217b65c" class="">All system messages, notifications, validation warnings, and status alerts must use the <strong>custom Toaster effect</strong>—no window alerts or legacy snackbars.</p><p id="2312881f-86f1-804a-acc9-d59ab8746ab3" class=""><strong>Implementation Steps:</strong></p><ul id="2312881f-86f1-802f-a70a-d6edf318aacf" class="bulleted-list"><li style="list-style-type:disc"><strong>Step 1:</strong><ul id="2312881f-86f1-80ae-adb3-d2821f7f9b67" class="bulleted-list"><li style="list-style-type:circle">Place the <code>&lt;Toaster /&gt;</code> component at the app’s root (usually in <code>/app/layout.tsx</code>).</li></ul><ul id="2312881f-86f1-8096-87b6-e5d7438e4efa" class="bulleted-list"><li style="list-style-type:circle">Reference code as provided (see above for import/useRef).</li></ul></li></ul><ul id="2312881f-86f1-805e-9b87-f14f9ec90313" class="bulleted-list"><li style="list-style-type:disc"><strong>Step 2:</strong><ul id="2312881f-86f1-8065-8164-e7bdb37b4366" class="bulleted-list"><li style="list-style-type:circle">Wrap the app in a <code>ToasterProvider</code> to make toaster ref available globally.</li></ul><ul id="2312881f-86f1-80b5-9b03-f80cf4d8c82b" class="bulleted-list"><li style="list-style-type:circle">Use the <code>useToaster()</code> hook to trigger notifications from any component or tool.</li></ul></li></ul><ul id="2312881f-86f1-80ac-9ec6-f11e3ce25bda" class="bulleted-list"><li style="list-style-type:disc"><strong>Step 3:</strong><ul id="2312881f-86f1-80a7-8f12-d268b014731b" class="bulleted-list"><li style="list-style-type:circle">Replace all alert/info/error/warning messages with <code>toasterRef.current?.show({ ... })</code></li></ul><ul id="2312881f-86f1-8054-b85c-d71a7d7e364d" class="bulleted-list"><li style="list-style-type:circle">Use variants: <code>&quot;success&quot;</code>, <code>&quot;warning&quot;</code>, <code>&quot;error&quot;</code>, <code>&quot;default&quot;</code> as context requires.</li></ul><ul id="2312881f-86f1-80f8-8c18-c2fc565b3253" class="bulleted-list"><li style="list-style-type:circle">Add action buttons when user remediation is possible (“Fix Input”, “Retry”, etc).</li></ul></li></ul><ul id="2312881f-86f1-80fa-9d33-d420398b642b" class="bulleted-list"><li style="list-style-type:disc"><strong>Step 4:</strong><ul id="2312881f-86f1-80e5-a7a0-cf55d24d87c8" class="bulleted-list"><li style="list-style-type:circle">Test in all supported color themes and ensure only one or two toasts show at once.</li></ul></li></ul><ul id="2312881f-86f1-8013-89b2-f27b2cb80af6" class="bulleted-list"><li style="list-style-type:disc"><strong>Code Samples:</strong><ul id="2312881f-86f1-8058-bad9-d2ade7c13099" class="bulleted-list"><li style="list-style-type:circle">See code blocks in your message for direct integration (Dock, Toaster, Button).</li></ul></li></ul><hr id="2312881f-86f1-8091-8aee-cfdc13b7b215"/><h3 id="2312881f-86f1-8000-8424-f6322b45ff6d" class="">3. <strong>General Instructions</strong></h3><ul id="2312881f-86f1-8022-aee5-c4b7a9e9c224" class="bulleted-list"><li style="list-style-type:disc"><strong>Accessibility:</strong><ul id="2312881f-86f1-80fe-aed2-c0be98354aa3" class="bulleted-list"><li style="list-style-type:circle">All navigation and notifications must be keyboard and screen-reader accessible.</li></ul></li></ul><ul id="2312881f-86f1-80fe-be8f-d65357482569" class="bulleted-list"><li style="list-style-type:disc"><strong>Consistency:</strong><ul id="2312881f-86f1-8065-b165-dd3053379d4e" class="bulleted-list"><li style="list-style-type:circle">Use these effects everywhere (across all tools/modules).</li></ul><ul id="2312881f-86f1-80de-bad3-f07bf7c1a6b1" class="bulleted-list"><li style="list-style-type:circle">Do not mix with older notification styles.</li></ul></li></ul><ul id="2312881f-86f1-808e-bfcc-d68b2efd8472" class="bulleted-list"><li style="list-style-type:disc"><strong>Testing:</strong><ul id="2312881f-86f1-80f4-a759-e0a35e697aad" class="bulleted-list"><li style="list-style-type:circle">QA across all major browsers, desktop, and tablet/iPad layouts.</li></ul></li></ul><ul id="2312881f-86f1-80e8-99b3-cc8d1e265474" class="bulleted-list"><li style="list-style-type:disc"><strong>Customization:</strong><ul id="2312881f-86f1-806e-8824-d440ba614f05" class="bulleted-list"><li style="list-style-type:circle">If admin-only override or custom themes are needed, consult design/system team before extending.</li></ul></li></ul><hr id="2312881f-86f1-8079-9b6a-c831bc11f6d9"/><h2 id="2312881f-86f1-80bb-8de9-d78e8c64d323" class=""><strong>Summary</strong></h2><ul id="2312881f-86f1-8090-afbe-dfc1f66e5540" class="bulleted-list"><li style="list-style-type:disc"><strong>Apply Dock “label-at-hover” animation for all main top navigation items.</strong></li></ul><ul id="2312881f-86f1-8054-b6bd-e61fa18f7902" class="bulleted-list"><li style="list-style-type:disc"><strong>Implement universal animated Toaster (bottom-right) for all notifications and user feedback.</strong></li></ul><ul id="2312881f-86f1-800d-ae55-dc1a6fc2033f" class="bulleted-list"><li style="list-style-type:disc"><strong>Ensure code samples provided are referenced and used as implementation baseline.</strong></li></ul><ul id="2312881f-86f1-80dd-b059-fc298a62af40" class="bulleted-list"><li style="list-style-type:disc"><strong>Phase out all legacy alerts/snackbars in favor of these new, unified effects.</strong></li></ul><hr id="2312881f-86f1-801b-bc0d-f2196e9fc176"/><p id="2312881f-86f1-808b-9eae-e53bc2d4869d" class=""><strong>If any part of the code or workflow integration is unclear, ask for clarification or further breakdown per tool or screen.</strong></p></details></li></ul></div></div><div id="2312881f-86f1-80df-b0f2-e05d364f8e60" class="column-list"><div id="2312881f-86f1-8096-8181-d9415ced9125" style="width:50%" class="column"><ul id="2312881f-86f1-8066-bee9-e2d8fd4a08bd" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Theme</span><div class="indented"></div></li></ul></div><div id="2312881f-86f1-8023-acb3-df2b072323ed" style="width:50%" class="column"><ul id="2312881f-86f1-806b-a09d-f64d8f48f124" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Light Mode - White</span><div class="indented"></div></li></ul><ul id="2312881f-86f1-80ba-902d-f709a100e762" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Dark Mode - Black</span><div class="indented"></div></li></ul><ul id="2312881f-86f1-806e-aad1-cde43c54bd75" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">System - Dark/Light Mode</span><div class="indented"></div></li></ul></div></div></details></li></ul></div></details><details open=""><summary style="font-weight:600;font-size:1.25em;line-height:1.3;margin:0">SizeWise Home Page</summary><div class="indented"><ul id="2312881f-86f1-804e-ab48-f8f228f544e3" class="toggle"><li><details open=""><summary>Background</summary><ul id="2312881f-86f1-80b7-9940-cfecdfa04029" class="toggle"><li><details open=""><summary>Background Effects</summary><ul id="2312881f-86f1-804e-994b-ca430f813d0f" class="toggle"><li><details open=""><summary>Laser Background</summary><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-80c2-9555-cf48505dff0c" class="code"><code class="language-TypeScript"></code></pre></details></li></ul><ul id="2312881f-86f1-80f5-ac6b-f3e4f3542394" class="toggle"><li><details open=""><summary>Black Background Color</summary><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-802c-a9ab-ca3c266ee82f" class="code"><code class="language-TypeScript"></code></pre></details></li></ul></details></li></ul></details></li></ul></div></details><details open=""><summary style="font-weight:600;font-size:1.25em;line-height:1.3;margin:0">Air Duct Sizer</summary><div class="indented"><ul id="2312881f-86f1-8045-be1a-d5b088bb64d4" class="toggle"><li><details open=""><summary><strong>Canvas, Panel &amp; Interaction Specs</strong></summary><h3 id="2332881f-86f1-80d5-b1bb-f095a54042d3" class=""><strong>Air Duct Sizer 3D Tool — Canvas, Panel &amp; Interaction Specification</strong></h3><hr id="2332881f-86f1-80c5-bc0a-e75c740c5e9a"/><ul id="2332881f-86f1-8026-ad4c-e6a80476d3e7" class="toggle"><li><details open=""><summary><strong>UI Placement Summary Table</strong></summary><table id="2332881f-86f1-8018-90b0-fb52252c470f" class="simple-table"><thead class="simple-table-header"><tr id="2332881f-86f1-807c-9f1f-f6d314c35a2f"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">Element ID</th><th id="J}@?" class="simple-table-header-color simple-table-header" style="width:104px">Component</th><th id="S&gt;k}" class="simple-table-header-color simple-table-header" style="width:104px">Type</th><th id=";x@\" class="simple-table-header-color simple-table-header" style="width:104px">Placement</th><th id="XFT@" class="simple-table-header-color simple-table-header" style="width:104px">Visibility</th><th id="?m{q" class="simple-table-header-color simple-table-header" style="width:104px">Purpose</th></tr></thead><tbody><tr id="2332881f-86f1-80a0-b02d-ea403b2d3c3f"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">1</th><td id="J}@?" class="" style="width:104px"><strong>Project Properties</strong></td><td id="S&gt;k}" class="" style="width:104px">Retractable panel</td><td id=";x@\" class="" style="width:104px">Top-left</td><td id="XFT@" class="" style="width:104px">On demand</td><td id="?m{q" class="" style="width:104px">Project metadata</td></tr><tr id="2332881f-86f1-800d-9eda-eddcaf90f0c9"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">2</th><td id="J}@?" class="" style="width:104px"><strong>3D Canvas</strong></td><td id="S&gt;k}" class="" style="width:104px">Workspace</td><td id=";x@\" class="" style="width:104px">Full center viewport</td><td id="XFT@" class="" style="width:104px">Always</td><td id="?m{q" class="" style="width:104px">Drawing stick lines &amp; interacting with 3D ducts</td></tr><tr id="2332881f-86f1-80a9-93ab-eee76ab7550b"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">3</th><td id="J}@?" class="" style="width:104px"><strong>Drawing Tool Pencil FAB</strong></td><td id="S&gt;k}" class="" style="width:104px">Toggle button</td><td id=";x@\" class="" style="width:104px">Bottom-right</td><td id="XFT@" class="" style="width:104px">Always</td><td id="?m{q" class="" style="width:104px">Draw stick lines ON/OFF</td></tr><tr id="2332881f-86f1-8028-8a13-ff44a394e31d"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">4</th><td id="J}@?" class="" style="width:104px"><strong>Convert to 3D Ducts</strong></td><td id="S&gt;k}" class="" style="width:104px">Primary button</td><td id=";x@\" class="" style="width:104px">Bottom-right cluster or Calc Bar</td><td id="XFT@" class="" style="width:104px">On demand</td><td id="?m{q" class="" style="width:104px">Convert stick lines to 3D duct models</td></tr><tr id="2332881f-86f1-80ca-bf05-f36a39dfca78"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">5</th><td id="J}@?" class="" style="width:104px"><strong>Selection Pop-Up</strong></td><td id="S&gt;k}" class="" style="width:104px">Contextual window</td><td id=";x@\" class="" style="width:104px">Anchored near element</td><td id="XFT@" class="" style="width:104px">On selection</td><td id="?m{q" class="" style="width:104px">Quick edit/properties</td></tr><tr id="2332881f-86f1-8070-817b-c5342f3641ec"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">6</th><td id="J}@?" class="" style="width:104px"><strong>Calculation Bar</strong></td><td id="S&gt;k}" class="" style="width:104px">Persistent bar</td><td id=";x@\" class="" style="width:104px">Bottom full-width</td><td id="XFT@" class="" style="width:104px">Always</td><td id="?m{q" class="" style="width:104px">Key results &amp; quick actions</td></tr><tr id="2332881f-86f1-806f-99e6-eb7bacb632f5"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">7</th><td id="J}@?" class="" style="width:104px"><strong>Import/Export Panel</strong></td><td id="S&gt;k}" class="" style="width:104px">Collapsible panel</td><td id=";x@\" class="" style="width:104px">Above Calculation Bar</td><td id="XFT@" class="" style="width:104px">On demand</td><td id="?m{q" class="" style="width:104px">Manage files</td></tr><tr id="2332881f-86f1-8044-b12b-d9a0e55ed280"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">8</th><td id="J}@?" class="" style="width:104px"><strong>Warning Panel</strong></td><td id="S&gt;k}" class="" style="width:104px">Retractable panel</td><td id=";x@\" class="" style="width:104px">Right edge viewport</td><td id="XFT@" class="" style="width:104px">On demand</td><td id="?m{q" class="" style="width:104px">List and resolve warnings</td></tr><tr id="2332881f-86f1-8066-95f3-f92656025f4a"><th id="oTj[" class="simple-table-header-color simple-table-header" style="width:104px">9</th><td id="J}@?" class="" style="width:104px"><strong>View Cube</strong></td><td id="S&gt;k}" class="" style="width:104px">Navigation aid</td><td id=";x@\" class="" style="width:104px">Top-right</td><td id="XFT@" class="" style="width:104px">Always</td><td id="?m{q" class="" style="width:104px">3D orientation</td></tr></tbody></table></details></li></ul><hr id="2332881f-86f1-80b7-816c-c5514fc96dcc"/><ul id="2332881f-86f1-8033-beeb-f960f84e217c" class="toggle"><li><details open=""><summary><strong>1. Project Properties Panel</strong></summary><h1 id="2352881f-86f1-806f-bccf-f8fc0460c553" class=""><strong>Project Properties Panel — Complete Documentation</strong></h1><hr id="2352881f-86f1-8026-90b0-f30ca253221a"/><h2 id="2352881f-86f1-80fd-946c-f8b6c9148dd9" class=""><strong>A. Overview &amp; Rationale</strong></h2><p id="2352881f-86f1-8004-a6aa-c99914dc4e92" class="">The <strong>Project Properties Panel</strong> provides a single, discoverable entry point for managing all project-wide data, settings, and metadata. Its design minimizes workspace intrusion, maximizes accessibility, and matches modern professional UI patterns found in top SaaS and CAD tools.</p><hr id="2352881f-86f1-8003-b531-db8cda4dcab1"/><h2 id="2352881f-86f1-806f-8335-ea029760b769" class=""><strong>B. Collapsed State (“Retracted”)</strong></h2><h3 id="2352881f-86f1-802e-b42f-e41c2d4290b5" class=""><strong>Trigger Button</strong></h3><ul id="2352881f-86f1-8034-9ff8-ee6b1bdc9e61" class="bulleted-list"><li style="list-style-type:disc"><strong>Location:</strong> Top-left, docked flush to the viewport’s left/top edge, always visible.</li></ul><ul id="2352881f-86f1-80dc-ab24-d1f028a25b98" class="bulleted-list"><li style="list-style-type:disc"><strong>Dimensions:</strong> 44 px tall × 56 px wide (icon only, default) or up to 160 px (icon + label on wider screens).</li></ul><ul id="2352881f-86f1-8017-b2d0-e426d09feb83" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong><ul id="2352881f-86f1-8065-9dfa-ea2df3134495" class="bulleted-list"><li style="list-style-type:circle"><strong>Folder</strong> (<code>folder_open</code> recommended, Material/Apple style, filled for modern look).</li></ul><ul id="2352881f-86f1-808f-8b0d-c6c2cb1a825d" class="bulleted-list"><li style="list-style-type:circle"><strong>Color:</strong><ul id="2352881f-86f1-80c8-aecc-dc35fa911e6a" class="bulleted-list"><li style="list-style-type:square">Default: #90A4AE (cool gray)</li></ul><ul id="2352881f-86f1-80d1-9b03-e0b9e6f5c4c1" class="bulleted-list"><li style="list-style-type:square">Hover/Focus: background increases opacity to rgba(255,255,255,0.96), folder icon gets a subtle #FF9800 (orange) glow.</li></ul><ul id="2352881f-86f1-803c-a5f2-efe4003e9284" class="bulleted-list"><li style="list-style-type:square">Active (panel open): icon highlights #FF9800, folder background stays frosted.</li></ul></li></ul></li></ul><ul id="2352881f-86f1-8065-985a-fd45f11a30dc" class="bulleted-list"><li style="list-style-type:disc"><strong>Label (Optional):</strong><ul id="2352881f-86f1-80e0-a40e-ddc945a5db5a" class="bulleted-list"><li style="list-style-type:circle">Text: “Project” (15–16 px, semi-bold, #424242)</li></ul><ul id="2352881f-86f1-80f5-ab2f-d0dd1341c387" class="bulleted-list"><li style="list-style-type:circle">Visible beside icon if enough space.</li></ul></li></ul><ul id="2352881f-86f1-80bf-a90b-e77b146d2d34" class="bulleted-list"><li style="list-style-type:disc"><strong>Background:</strong><ul id="2352881f-86f1-80b4-a2f1-f08053a09587" class="bulleted-list"><li style="list-style-type:circle">Frosted glass: rgba(255,255,255,0.92), border #E0E0E0 (2 px, 25% opacity).</li></ul><ul id="2352881f-86f1-800c-b6bb-f44cffde26dc" class="bulleted-list"><li style="list-style-type:circle">Corners: top-left 16 px, others 0 px.</li></ul><ul id="2352881f-86f1-8003-ba21-dfb11046e226" class="bulleted-list"><li style="list-style-type:circle">Subtle shadow: 0 1px 8px rgba(180,190,200,0.10)</li></ul></li></ul><ul id="2352881f-86f1-80ac-83c7-f9247a7045da" class="bulleted-list"><li style="list-style-type:disc"><strong>Affordance:</strong><ul id="2352881f-86f1-8075-bc67-d5f999fc9423" class="bulleted-list"><li style="list-style-type:circle">Cursor: pointer on hover</li></ul><ul id="2352881f-86f1-8037-8101-fd1be0c59cb9" class="bulleted-list"><li style="list-style-type:circle">Tooltip: “Project Properties”</li></ul><ul id="2352881f-86f1-8047-92a4-cb9fc5de72d4" class="bulleted-list"><li style="list-style-type:circle">ARIA label: <code>aria-label=&quot;Open Project Properties Panel&quot;</code></li></ul><ul id="2352881f-86f1-8059-b667-f48a6ac29bee" class="bulleted-list"><li style="list-style-type:circle">Tab/Enter/Space opens panel</li></ul></li></ul><hr id="2352881f-86f1-806b-ac4e-e3af677e5f91"/><h2 id="2352881f-86f1-80ee-acba-ed04be2d124a" class=""><strong>C. Expanded State (“Open”)</strong></h2><h3 id="2352881f-86f1-809d-aeb3-fe6a6ca103f6" class=""><strong>Panel Layout &amp; Appearance</strong></h3><ul id="2352881f-86f1-8020-92d4-fa09fa0019c2" class="bulleted-list"><li style="list-style-type:disc"><strong>Expansion Animation:</strong><ul id="2352881f-86f1-8073-aa61-c9f5dd3e7f93" class="bulleted-list"><li style="list-style-type:circle">Panel slides in from the left (≤200 ms, cubic-ease), icon/label animate to “pressed” state.</li></ul></li></ul><ul id="2352881f-86f1-8096-8068-cf8a21cc9939" class="bulleted-list"><li style="list-style-type:disc"><strong>Dimensions:</strong><ul id="2352881f-86f1-801f-93cc-d9282c7e96f2" class="bulleted-list"><li style="list-style-type:circle">Width: 320–360 px (responsive, never collapses thinner than 320 px)</li></ul><ul id="2352881f-86f1-8051-87d4-e23566e61bb0" class="bulleted-list"><li style="list-style-type:circle">Height: 100% viewport (full vertical)</li></ul></li></ul><ul id="2352881f-86f1-8083-835d-db2b850f1bd7" class="bulleted-list"><li style="list-style-type:disc"><strong>Background:</strong><ul id="2352881f-86f1-808c-9e3d-fe1657fa49fb" class="bulleted-list"><li style="list-style-type:circle">Frosted glass (rgba(255,255,255,0.92)), same as collapsed</li></ul><ul id="2352881f-86f1-80d9-bf58-db2b0f595c2a" class="bulleted-list"><li style="list-style-type:circle">Liquid glass border (#E0E0E0), 2 px, 25% opacity, top-left 16 px</li></ul><ul id="2352881f-86f1-802d-9cad-d78842869fe2" class="bulleted-list"><li style="list-style-type:circle">Subtle drop shadow (0 2px 24px #B0BEC5)</li></ul></li></ul><ul id="2352881f-86f1-8094-bcf0-edce581873d6" class="bulleted-list"><li style="list-style-type:disc"><strong>Header:</strong><ul id="2352881f-86f1-803c-9e76-db8f3143ec48" class="bulleted-list"><li style="list-style-type:circle">44–56 px tall: folder icon (now orange #FF9800), label “Project Properties,” and [×] close button at right</li></ul><ul id="2352881f-86f1-80fe-a409-f6d8c5557716" class="bulleted-list"><li style="list-style-type:circle">Header background: slightly more opaque (for focus), no shadow</li></ul></li></ul><hr id="2352881f-86f1-8035-b81b-c5eb2660303e"/><h3 id="2352881f-86f1-80d6-90c3-f8b8a95c1d84" class=""><strong>D. Contents &amp; Organization</strong></h3><h3 id="2352881f-86f1-80ac-afa3-c2c1a9798482" class=""><strong>Section A: Project Information</strong> <em>(Always expanded)</em></h3><ul id="2352881f-86f1-80d9-9f04-d971bad2ec4b" class="bulleted-list"><li style="list-style-type:disc"><strong>Project Name</strong> (text input)</li></ul><ul id="2352881f-86f1-8017-9507-c1af334ca2ec" class="bulleted-list"><li style="list-style-type:disc"><strong>Project Number</strong> (text input)</li></ul><ul id="2352881f-86f1-80d3-ab8d-d749b4d289c5" class="bulleted-list"><li style="list-style-type:disc"><strong>Project Description</strong> (multiline textarea)</li></ul><ul id="2352881f-86f1-80bc-b9b5-e08e15e7d7f4" class="bulleted-list"><li style="list-style-type:disc"><strong>Project Location</strong> (text input or dropdown)</li></ul><ul id="2352881f-86f1-80bf-890d-ce43e8ff591f" class="bulleted-list"><li style="list-style-type:disc"><strong>Client Name</strong> (text input)</li></ul><ul id="2352881f-86f1-80a7-94d2-f1dd091edccd" class="bulleted-list"><li style="list-style-type:disc"><strong>Estimator Name</strong> (auto-filled, editable)</li></ul><ul id="2352881f-86f1-8001-90a8-fee2457e9da8" class="bulleted-list"><li style="list-style-type:disc"><strong>Date Created</strong> (read-only)</li></ul><ul id="2352881f-86f1-80a3-a02c-cbb787a9d6e3" class="bulleted-list"><li style="list-style-type:disc"><strong>Last Modified</strong> (read-only)</li></ul><ul id="2352881f-86f1-800f-a574-f83b960f190c" class="bulleted-list"><li style="list-style-type:disc"><strong>Version</strong> (read-only, links to Version Control if available)</li></ul><ul id="2352881f-86f1-8014-b945-cecce6df9ce0" class="bulleted-list"><li style="list-style-type:disc"><strong>Company Logo</strong> (upload field, square with rounded corner preview 40×40 px)</li></ul><h3 id="2352881f-86f1-803a-919e-c3bbfcaaf2bd" class=""><strong>Section B: Code References</strong> <em>(Collapsible dropdown)</em></h3><ul id="2352881f-86f1-801f-8301-c534207a2234" class="bulleted-list"><li style="list-style-type:disc"><strong>Duct Sizing Standard</strong> (dropdown: SMACNA, ASHRAE, Local, etc.)</li></ul><ul id="2352881f-86f1-80d5-bf79-f27135840eec" class="bulleted-list"><li style="list-style-type:disc"><strong>Material Standard</strong> (dropdown or multi-select)</li></ul><ul id="2352881f-86f1-8007-b5ac-c6f9e9a1881f" class="bulleted-list"><li style="list-style-type:disc"><strong>Fire Safety Standard</strong> (dropdown: UL, NFPA, Local, etc.)</li></ul><ul id="2352881f-86f1-80ad-862e-e153120d0eb9" class="bulleted-list"><li style="list-style-type:disc"><strong>Local Regulations</strong> (text input)</li></ul><ul id="2352881f-86f1-80d8-9e84-e0ae3a698bf8" class="bulleted-list"><li style="list-style-type:disc"><strong>Import Standards</strong> (button)</li></ul><ul id="2352881f-86f1-80cb-a750-e9fc69019dd0" class="bulleted-list"><li style="list-style-type:disc"><strong>Export Standards</strong> (button)</li></ul><h3 id="2352881f-86f1-80b5-8027-cae253dc4d1b" class=""><strong>Section C: Global Defaults</strong> <em>(Collapsible dropdown)</em></h3><ul id="2352881f-86f1-80f8-8235-dbaddaac26cb" class="bulleted-list"><li style="list-style-type:disc"><strong>Measurement Units</strong> (dropdown: Imperial/Metric)</li></ul><ul id="2352881f-86f1-80e5-9709-c2ba000a5a99" class="bulleted-list"><li style="list-style-type:disc"><strong>Default Duct Size</strong> (width × height or diameter, numeric input, 8” default if no input from user)</li></ul><ul id="2352881f-86f1-8071-b918-dce85a56343f" class="bulleted-list"><li style="list-style-type:disc"><strong>Default Material</strong> (dropdown)</li></ul><ul id="2352881f-86f1-8031-b789-d77633fcaa3a" class="bulleted-list"><li style="list-style-type:disc"><strong>Default Insulation Type</strong> (dropdown)</li></ul><ul id="2352881f-86f1-80b8-b8d0-d733d170cc59" class="bulleted-list"><li style="list-style-type:disc"><strong>Default Fitting Type</strong> (dropdown)</li></ul><ul id="2352881f-86f1-80c5-aff2-d2019b0c03ce" class="bulleted-list"><li style="list-style-type:disc"><strong>Calibration Mode</strong> (toggle: Auto/Manual)</li></ul><ul id="2352881f-86f1-800e-9ca3-d20ef6ff5f85" class="bulleted-list"><li style="list-style-type:disc"><strong>Design Parameters</strong> (custom fields, optional)</li></ul><h3 id="2352881f-86f1-8034-bda1-e712fa7705ea" class=""><strong>Section D: Team &amp; Collaboration</strong> <em>(Collapsible dropdown)</em></h3><ul id="2352881f-86f1-8021-b629-ca8357d9ea41" class="bulleted-list"><li style="list-style-type:disc"><strong>Project Owner</strong> (avatar/name, dropdown reassign)</li></ul><ul id="2352881f-86f1-8025-ba54-f1f4110e557e" class="bulleted-list"><li style="list-style-type:disc"><strong>Team Members</strong> (list, add/remove, assign roles)</li></ul><ul id="2352881f-86f1-8077-97e2-ea4fe720f7bb" class="bulleted-list"><li style="list-style-type:disc"><strong>User Roles</strong> (per member: Editor, Viewer, Admin)</li></ul><ul id="2352881f-86f1-80f3-a5b3-f2e96018e853" class="bulleted-list"><li style="list-style-type:disc"><strong>Share Project</strong> (toggle)</li></ul><ul id="2352881f-86f1-805e-b0ff-efba727b5620" class="bulleted-list"><li style="list-style-type:disc"><strong>Activity Log</strong> (read-only, scrollable)</li></ul><h3 id="2352881f-86f1-8080-8d08-f16aa7414c14" class=""><strong>Section E: Project Administration</strong> <em>(Collapsible dropdown)</em></h3><ul id="2352881f-86f1-8010-985e-e5ed83c0bd16" class="bulleted-list"><li style="list-style-type:disc"><strong>Project Status</strong> (dropdown: Design, Bid, Construction, As-Built)</li></ul><ul id="2352881f-86f1-8048-9bd2-f729063c8b06" class="bulleted-list"><li style="list-style-type:disc"><strong>Project Notes</strong> (rich textarea, time-stamped)</li></ul><ul id="2352881f-86f1-8068-bf45-f9d5aed8eb99" class="bulleted-list"><li style="list-style-type:disc"><strong>Stakeholder Contacts</strong> (Name, Role, Email, Phone—repeatable fields)</li></ul><ul id="2352881f-86f1-803e-bf25-e2a4b5c70057" class="bulleted-list"><li style="list-style-type:disc"><strong>Custom Fields</strong> (user-defined label/value)</li></ul><ul id="2352881f-86f1-80af-8f70-e1a3518ca575" class="bulleted-list"><li style="list-style-type:disc"><strong>Archive Project</strong> (button; confirmation dialog opens)</li></ul><ul id="2352881f-86f1-80a5-ae05-e12a54bcbc65" class="bulleted-list"><li style="list-style-type:disc"><strong>Export Project</strong> (button)</li></ul><hr id="2352881f-86f1-8083-ac81-f0c579bec140"/><h3 id="2352881f-86f1-80be-a579-dd4c5330c021" class=""><strong>E. Interaction Details</strong></h3><ul id="2352881f-86f1-8024-9f49-e8fe035a004b" class="bulleted-list"><li style="list-style-type:disc"><strong>Expand/collapse:</strong><ul id="2352881f-86f1-804a-8c6a-e123624ac69a" class="bulleted-list"><li style="list-style-type:circle">Clicking a section header toggles open/closed (arrow rotates, section animates &lt;150 ms)</li></ul></li></ul><ul id="2352881f-86f1-807c-95d3-cfc00c50cca1" class="bulleted-list"><li style="list-style-type:disc"><strong>Inputs:</strong><ul id="2352881f-86f1-8006-a3cf-ed6b96c78f11" class="bulleted-list"><li style="list-style-type:circle">All fields use “liquid glass” styling—subtle transparent white, sharp contrast text, rounded corners.</li></ul></li></ul><ul id="2352881f-86f1-80b3-9416-f02d8b3a5f1c" class="bulleted-list"><li style="list-style-type:disc"><strong>Movability:</strong><ul id="2352881f-86f1-80e7-95bb-d311c09ebcbe" class="bulleted-list"><li style="list-style-type:circle">Panel is fixed—cannot be moved or detached.</li></ul></li></ul><ul id="2352881f-86f1-8095-b57b-ee8b38d11515" class="bulleted-list"><li style="list-style-type:disc"><strong>Dismissal:</strong><ul id="2352881f-86f1-8078-8b12-e77af5a36e08" class="bulleted-list"><li style="list-style-type:circle">Click [×] in header, click outside panel, or press Esc to close.</li></ul><ul id="2352881f-86f1-80a5-942d-f5f2843e56c3" class="bulleted-list"><li style="list-style-type:circle">Panel always returns to last scroll position when reopened.</li></ul></li></ul><ul id="2352881f-86f1-80a1-b689-deba2ec80731" class="bulleted-list"><li style="list-style-type:disc"><strong>Accessibility:</strong><ul id="2352881f-86f1-8080-9331-ff83de33ae60" class="bulleted-list"><li style="list-style-type:circle">All fields and headers are tab-navigable.</li></ul><ul id="2352881f-86f1-8018-8c88-f638b808c29c" class="bulleted-list"><li style="list-style-type:circle">Section headers announce “expand/collapse” to screen readers.</li></ul><ul id="2352881f-86f1-80dc-8fbc-fcd0abed3d85" class="bulleted-list"><li style="list-style-type:circle">Close button is last in tab sequence.</li></ul></li></ul><hr id="2352881f-86f1-80af-b672-f610a5c365e0"/><h3 id="2352881f-86f1-8000-ac83-f3ec65370eaa" class=""><strong>F. Visual &amp; Theming Details</strong></h3><ul id="2352881f-86f1-80eb-b00b-d1cc24fc28d1" class="bulleted-list"><li style="list-style-type:disc"><strong>Panel BG:</strong> rgba(255,255,255,0.92)</li></ul><ul id="2352881f-86f1-808a-b66f-d9f087959a65" class="bulleted-list"><li style="list-style-type:disc"><strong>Border:</strong> #E0E0E0, 2 px</li></ul><ul id="2352881f-86f1-801e-8086-f83275ae820c" class="bulleted-list"><li style="list-style-type:disc"><strong>Input BG:</strong> rgba(255,255,255,0.82), border #D1D1D1</li></ul><ul id="2352881f-86f1-809e-aea5-f14a79dc50f4" class="bulleted-list"><li style="list-style-type:disc"><strong>Active icon:</strong> #FF9800 (orange highlight)</li></ul><ul id="2352881f-86f1-806e-ae7a-fa3bcac861c3" class="bulleted-list"><li style="list-style-type:disc"><strong>Text:</strong> #212121 (90% opacity)</li></ul><ul id="2352881f-86f1-809b-8318-fe9fdda523eb" class="bulleted-list"><li style="list-style-type:disc"><strong>Headers:</strong> #424242, semi-bold, 16–17 px</li></ul><ul id="2352881f-86f1-8042-8528-e43372c2dbc8" class="bulleted-list"><li style="list-style-type:disc"><strong>Labels:</strong> #616161, regular, 15 px</li></ul><ul id="2352881f-86f1-80da-b0d8-ebe97918d1de" class="bulleted-list"><li style="list-style-type:disc"><strong>Dropdowns/Buttons:</strong> Slightly raised with liquid glass effect; clear hover/active states</li></ul><hr id="2352881f-86f1-80ef-bc72-edb22c907cbd"/><h3 id="2352881f-86f1-80d0-be82-c310ddd78d3a" class=""><strong>G. Summary Table</strong></h3><table id="2352881f-86f1-8043-bec7-dc31c084164d" class="simple-table"><thead class="simple-table-header"><tr id="2352881f-86f1-809a-b31b-c1e9f853d7a6"><th id="yGZ`" class="simple-table-header-color simple-table-header">Element</th><th id="VNkk" class="simple-table-header-color simple-table-header">State</th><th id="tEMC" class="simple-table-header-color simple-table-header">Description</th></tr></thead><tbody><tr id="2352881f-86f1-807a-93b0-f311e15cc49a"><td id="yGZ`" class="">Trigger Button (Panel)</td><td id="VNkk" class="">Collapsed</td><td id="tEMC" class="">Top-left, folder icon, frosted glass, 44×56 px</td></tr><tr id="2352881f-86f1-800c-98e4-d6bd40de542b"><td id="yGZ`" class="">Panel (Main)</td><td id="VNkk" class="">Expanded</td><td id="tEMC" class="">320–360 px wide, 100% tall, frosted, 5 section layout</td></tr><tr id="2352881f-86f1-806d-a9bc-f6f4d1a7197b"><td id="yGZ`" class="">Header</td><td id="VNkk" class="">Expanded</td><td id="tEMC" class="">Icon + label + close (×)</td></tr><tr id="2352881f-86f1-80fe-8b67-c518d7ff0324"><td id="yGZ`" class="">Section: Info</td><td id="VNkk" class="">Always open</td><td id="tEMC" class="">Name, Number, Client, Estimator, Logo, Dates, Version</td></tr><tr id="2352881f-86f1-80d6-b2bf-d39751487d50"><td id="yGZ`" class="">Section: Codes</td><td id="VNkk" class="">Collapsible</td><td id="tEMC" class="">Standards, regs, import/export</td></tr><tr id="2352881f-86f1-80f9-bb4e-f91bb1627429"><td id="yGZ`" class="">Section: Defaults</td><td id="VNkk" class="">Collapsible</td><td id="tEMC" class="">Units, material, fitting, calibration, params</td></tr><tr id="2352881f-86f1-80af-bf61-e8345e69a276"><td id="yGZ`" class="">Section: Team</td><td id="VNkk" class="">Collapsible</td><td id="tEMC" class="">Owner, team, sharing, log</td></tr><tr id="2352881f-86f1-806c-b2c1-fa923e586bc3"><td id="yGZ`" class="">Section: Admin</td><td id="VNkk" class="">Collapsible</td><td id="tEMC" class="">Status, notes, contacts, custom, archive/export</td></tr><tr id="2352881f-86f1-80fb-9f77-d19d645655b6"><td id="yGZ`" class="">Dismissal</td><td id="VNkk" class="">Any</td><td id="tEMC" class="">Esc, outside click, × button</td></tr><tr id="2352881f-86f1-8017-8764-d766b93a842b"><td id="yGZ`" class="">Accessibility</td><td id="VNkk" class="">Any</td><td id="tEMC" class="">Tab, ARIA, tooltips, contrast, keyboard</td></tr></tbody></table><hr id="2352881f-86f1-80cc-b4e8-e09527451c64"/><h2 id="2352881f-86f1-8036-87a0-e3f67bca8717" class=""><strong>H. Example Figma Wireframe Guidance</strong></h2><ul id="2352881f-86f1-8089-9fd5-f35ea8f04f06" class="bulleted-list"><li style="list-style-type:disc"><strong>Collapsed state:</strong> folder icon (left), label “Project” (if space), frosted glass, fixed top-left</li></ul><ul id="2352881f-86f1-80d7-9298-de3cd9b227a2" class="bulleted-list"><li style="list-style-type:disc"><strong>Expanded state:</strong> slides out over canvas, full-height, all sections laid out vertically, first section open, rest as dropdowns</li></ul><ul id="2352881f-86f1-80bc-aecb-e8caf8e42842" class="bulleted-list"><li style="list-style-type:disc"><strong>Inputs:</strong> liquid glass, iconography matches rest of app, consistent paddings (16 px horizontal, 20 px section heads)</li></ul><ul id="2352881f-86f1-80e6-8487-e1b8daf7e3dd" class="bulleted-list"><li style="list-style-type:disc"><strong>States:</strong> clear active, hover, focus for every field/button</li></ul><hr id="2352881f-86f1-80e5-a451-ea92c6d23cbf"/><h2 id="2352881f-86f1-802a-b19b-ef3d65a64b08" class=""><strong>I. Rationale &amp; UX Justification</strong></h2><ul id="2352881f-86f1-8016-8628-fa08ec330a20" class="bulleted-list"><li style="list-style-type:disc"><strong>One-click access</strong> for all project-level data</li></ul><ul id="2352881f-86f1-8094-bb67-e767d71964be" class="bulleted-list"><li style="list-style-type:disc"><strong>Non-intrusive</strong>: stays out of the way when not in use, instantly accessible when needed</li></ul><ul id="2352881f-86f1-8010-8545-c1de4695b4cc" class="bulleted-list"><li style="list-style-type:disc"><strong>Scalable</strong>: new fields, standards, and metadata can be added via custom fields</li></ul><ul id="2352881f-86f1-8046-8c27-c8753d1adb40" class="bulleted-list"><li style="list-style-type:disc"><strong>Professional, trusted look</strong> for engineers and PMs—evokes trust in data, easy to handoff for QA/compliance.</li></ul></details></li></ul><hr id="2332881f-86f1-804d-a0d9-dca0895bb0a5"/><ul id="2332881f-86f1-8081-bd61-e51eff4970e2" class="toggle"><li><details open=""><summary><strong>2. 3D Canvas Workspace</strong></summary><h1 id="2352881f-86f1-8005-af24-e860cce21a59" class=""><strong>3D Canvas Workspace — Full Documentation</strong></h1><hr id="2352881f-86f1-80ba-93bc-f4f31df7671d"/><h2 id="2352881f-86f1-80a8-99e3-d4453ff3c16c" class=""><strong>A. Overview</strong></h2><p id="2352881f-86f1-803b-afe3-c077107296d0" class="">The <strong>3D Canvas Workspace</strong> is the heart of the Air Duct Sizer tool—serving as the interactive, visual area for creating, editing, and reviewing duct layouts and system models in real time.</p><hr id="2352881f-86f1-8027-b5f8-e94250c66a42"/><h2 id="2352881f-86f1-801e-94eb-f606b6014215" class=""><strong>B. Placement &amp; Appearance</strong></h2><ul id="2352881f-86f1-808f-a5ca-f5cb9edde3ed" class="bulleted-list"><li style="list-style-type:disc"><strong>Coverage:</strong><ul id="2352881f-86f1-808e-8b83-eae07291d86c" class="bulleted-list"><li style="list-style-type:circle">Occupies the entire main viewport except for overlaying panels, floating toolbars (FAB, View Cube, etc.), and status bars.</li></ul><ul id="2352881f-86f1-80ca-a365-d054d0142294" class="bulleted-list"><li style="list-style-type:circle"><strong>Always visible and maximized</strong>—no scrollbars unless the user zooms/pans.</li></ul></li></ul><ul id="2352881f-86f1-8047-9a48-c0beb0620ed8" class="bulleted-list"><li style="list-style-type:disc"><strong>Background:</strong><ul id="2352881f-86f1-80a5-9cd9-ed8fb58a1047" class="bulleted-list"><li style="list-style-type:circle">Clean, soft-white (#FAFBFC) for maximum contrast with drawn elements.</li></ul><ul id="2352881f-86f1-806e-9e4c-e7d25db9e26f" class="bulleted-list"><li style="list-style-type:circle">Subtle, non-intrusive grid (optional: #E3E7EA, 15% opacity) aids alignment.</li></ul></li></ul><ul id="2352881f-86f1-8069-b60f-f9337ce04438" class="bulleted-list"><li style="list-style-type:disc"><strong>Edges:</strong><ul id="2352881f-86f1-80b2-b474-dff8d935f02f" class="bulleted-list"><li style="list-style-type:circle">No borders, but a gentle drop shadow at the top and bottom edges may indicate workspace boundaries (optional for app style).</li></ul></li></ul><hr id="2352881f-86f1-8078-b8d8-ed98ab9fe445"/><h2 id="2352881f-86f1-80bf-8143-f54e89ebc44e" class=""><strong>C. Core Interactions</strong></h2><h3 id="2352881f-86f1-805d-a9ca-c842f24eb9e5" class=""><strong>1. Mouse and Touch Controls</strong></h3><h3 id="2352881f-86f1-80b5-8463-f5e5219a5096" class=""><strong>a. Navigation</strong></h3><ul id="2352881f-86f1-8008-a930-d5608d708044" class="bulleted-list"><li style="list-style-type:disc"><strong>Right-click + Drag:</strong><ul id="2352881f-86f1-8094-938c-f67f91e69473" class="bulleted-list"><li style="list-style-type:circle"><strong>Pan</strong> the view horizontally and vertically.</li></ul><ul id="2352881f-86f1-80bf-b398-c957199f2fba" class="bulleted-list"><li style="list-style-type:circle"><strong>Cursor:</strong> changes to “hand” while panning.</li></ul></li></ul><ul id="2352881f-86f1-806a-894f-eeebdf3b9b1d" class="bulleted-list"><li style="list-style-type:disc"><strong>Mouse Scroll Wheel / Two-finger Pinch (Touch):</strong><ul id="2352881f-86f1-8019-98aa-d1b119b9cae9" class="bulleted-list"><li style="list-style-type:circle"><strong>Zoom in/out</strong> centered on cursor position.</li></ul><ul id="2352881f-86f1-8057-9d41-d6d89bff844f" class="bulleted-list"><li style="list-style-type:circle"><strong>Zoom levels:</strong> min 10%, max 500%.</li></ul></li></ul><ul id="2352881f-86f1-8011-8fbf-c881bb400694" class="bulleted-list"><li style="list-style-type:disc"><strong>Left-click + Drag (Empty Space):</strong><ul id="2352881f-86f1-8076-80f9-c85eeb94534c" class="bulleted-list"><li style="list-style-type:circle"><strong>Rotate 3D Canvas:</strong><ul id="2352881f-86f1-8062-99c4-c30befa61a30" class="bulleted-list"><li style="list-style-type:square"><strong>Without modifier:</strong><ul id="2352881f-86f1-80cb-bee7-ff7ddaf1ba54" class="bulleted-list"><li style="list-style-type:disc">Rotates 3D view (orbit camera around model)</li></ul></li></ul><ul id="2352881f-86f1-800b-bf78-c4b696ae24ba" class="bulleted-list"><li style="list-style-type:square"><strong>With [Shift] key held:</strong><ul id="2352881f-86f1-803b-9cc8-c4e9e8991118" class="bulleted-list"><li style="list-style-type:disc">Restricts rotation to a single axis (horizontal/vertical based on drag direction)—enables “2D rotate” for precise plan/top/side views.</li></ul></li></ul></li></ul></li></ul><ul id="2352881f-86f1-8098-887b-e3b0ed265360" class="bulleted-list"><li style="list-style-type:disc"><strong>Double-click (Empty Space):</strong><ul id="2352881f-86f1-8060-b28b-d825372b60a9" class="bulleted-list"><li style="list-style-type:circle"><strong>Reset view</strong> to default orientation (top or isometric, configurable).</li></ul></li></ul><h3 id="2352881f-86f1-8044-b175-c940ced4b220" class=""><strong>b. Drawing vs. Selection</strong></h3><ul id="2352881f-86f1-8014-aa03-e163be4ee095" class="bulleted-list"><li style="list-style-type:disc"><strong>Pencil Tool OFF:</strong><ul id="2352881f-86f1-80d7-aea0-caba6efee826" class="bulleted-list"><li style="list-style-type:circle"><strong>Left-click on element:</strong> selects duct, fitting, or equipment.<ul id="2352881f-86f1-80ee-9b91-f647f48dd923" class="bulleted-list"><li style="list-style-type:square"><strong>Ctrl/Cmd+Click:</strong> multi-select.</li></ul></li></ul><ul id="2352881f-86f1-80ef-94a9-fae7ece6eb24" class="bulleted-list"><li style="list-style-type:circle"><strong>Left-click + drag (on empty):</strong> rotates canvas (see above).</li></ul></li></ul><ul id="2352881f-86f1-8063-872e-f5be49b9cfd0" class="bulleted-list"><li style="list-style-type:disc"><strong>Pencil Tool ON:</strong><ul id="2352881f-86f1-8033-a0ed-cfa01abbd71d" class="bulleted-list"><li style="list-style-type:circle"><strong>Left-click:</strong><ul id="2352881f-86f1-80a7-8837-eae9e7d44d34" class="bulleted-list"><li style="list-style-type:square">Click = Place new node for stick line.</li></ul><ul id="2352881f-86f1-8071-89b9-d22052d03706" class="bulleted-list"><li style="list-style-type:square">Double-click = End current duct run.</li></ul><ul id="2352881f-86f1-803d-b650-f0f6d6172ab3" class="bulleted-list"><li style="list-style-type:square">Right-click (while drawing) = temporarily pan without breaking line.</li></ul></li></ul><ul id="2352881f-86f1-80b6-ad77-cb24eb8db1a5" class="bulleted-list"><li style="list-style-type:circle"><strong>Esc key:</strong> cancels active drawing without placing the last segment.</li></ul></li></ul><hr id="2352881f-86f1-802f-89e4-c45439fb2016"/><h3 id="2352881f-86f1-806f-bb2c-fd159a15f188" class=""><strong>2. Keyboard Shortcuts</strong></h3><table id="************************************" class="simple-table"><thead class="simple-table-header"><tr id="2352881f-86f1-80bc-a718-dd16ec6f45f4"><th id=":yY=" class="simple-table-header-color simple-table-header">Shortcut</th><th id="&lt;[XR" class="simple-table-header-color simple-table-header">Action</th></tr></thead><tbody><tr id="2352881f-86f1-80d1-b08b-eb4f6c1a5edc"><td id=":yY=" class="">Esc</td><td id="&lt;[XR" class="">Cancel current drawing/selection</td></tr><tr id="2352881f-86f1-804a-a50b-e316c8d2baf3"><td id=":yY=" class="">Ctrl/Cmd + Z</td><td id="&lt;[XR" class="">Undo last action</td></tr><tr id="2352881f-86f1-8005-800f-fdbd3ba5975a"><td id=":yY=" class="">Ctrl/Cmd + Y</td><td id="&lt;[XR" class="">Redo last undone action</td></tr><tr id="2352881f-86f1-8086-b807-c3307b13ac5d"><td id=":yY=" class="">Ctrl/Cmd + A</td><td id="&lt;[XR" class="">Select all (if focus in canvas)</td></tr><tr id="2352881f-86f1-809a-850c-cfb9abe20eeb"><td id=":yY=" class="">Shift (hold)</td><td id="&lt;[XR" class="">Restrict rotation to single axis</td></tr><tr id="2352881f-86f1-8075-8946-f8359ae63036"><td id=":yY=" class="">Spacebar</td><td id="&lt;[XR" class="">Quick-toggle between select/draw tool</td></tr><tr id="2352881f-86f1-807b-9b0a-fb6982d29f74"><td id=":yY=" class="">F</td><td id="&lt;[XR" class="">Frame selected object(s)</td></tr></tbody></table><hr id="2352881f-86f1-800b-b476-d84aa04e5dc0"/><h3 id="2352881f-86f1-807f-bba2-ff84e14ea116" class=""><strong>3. Visual Feedback</strong></h3><ul id="2352881f-86f1-8065-8e1d-ffb78ee26f66" class="bulleted-list"><li style="list-style-type:disc"><strong>Selection:</strong><ul id="2352881f-86f1-80ae-89b4-f143889aa209" class="bulleted-list"><li style="list-style-type:circle">Selected elements outlined with high-contrast (e.g., #FF9800 orange glow) and “grip” handles for moving/resizing.</li></ul></li></ul><ul id="2352881f-86f1-803f-a5f4-c4988757a17f" class="bulleted-list"><li style="list-style-type:disc"><strong>Drawing Mode:</strong><ul id="2352881f-86f1-8003-8b4c-de178b887a4f" class="bulleted-list"><li style="list-style-type:circle">Current stick line shows as semi-transparent orange (#FFA726, 80% opacity).</li></ul><ul id="2352881f-86f1-80b0-b88a-e39a5e420c7f" class="bulleted-list"><li style="list-style-type:circle">Next node preview: ghosted circle at cursor.</li></ul></li></ul><ul id="2352881f-86f1-806f-b89b-cb7d849ac53e" class="bulleted-list"><li style="list-style-type:disc"><strong>Warnings:</strong><ul id="2352881f-86f1-80a1-a1f1-c1c15e358c78" class="bulleted-list"><li style="list-style-type:circle">Any duct/fitting with a warning: glows with red/yellow, matching severity.</li></ul></li></ul><ul id="2352881f-86f1-8046-be50-e696ad4176a2" class="bulleted-list"><li style="list-style-type:disc"><strong>Hover States:</strong><ul id="2352881f-86f1-8078-99c3-d0fd55ad9de5" class="bulleted-list"><li style="list-style-type:circle">Elements under cursor highlight (light blue #00BCD4, 40% opacity).</li></ul></li></ul><ul id="2352881f-86f1-80fb-8838-dd100e843b9d" class="bulleted-list"><li style="list-style-type:disc"><strong>Grid &amp; Origin:</strong><ul id="2352881f-86f1-8036-993b-f2a306c3a866" class="bulleted-list"><li style="list-style-type:circle">Optional grid fades in as user zooms closer; X/Y/Z origin marker always visible at bottom-left of canvas.</li></ul></li></ul><hr id="2352881f-86f1-80fa-84ea-e0268a410c47"/><h2 id="2352881f-86f1-8069-8ff0-d3007142a87e" class=""><strong>D. Accessibility</strong></h2><ul id="2352881f-86f1-80ea-9468-f755dedefd36" class="bulleted-list"><li style="list-style-type:disc"><strong>Tab navigation:</strong><ul id="2352881f-86f1-80fb-913d-d849e1f13ccd" class="bulleted-list"><li style="list-style-type:circle">All overlay/floating controls and context menus are focusable, but canvas itself is not tab-navigable (avoids trapping keyboard users).</li></ul></li></ul><ul id="2352881f-86f1-8051-a5eb-f1abd5e15457" class="bulleted-list"><li style="list-style-type:disc"><strong>Keyboard users:</strong><ul id="2352881f-86f1-8013-b75f-f248ba924cc6" class="bulleted-list"><li style="list-style-type:circle">All drawing, selection, and navigation accessible via keyboard shortcuts.</li></ul></li></ul><ul id="************************************" class="bulleted-list"><li style="list-style-type:disc"><strong>Screen reader:</strong><ul id="2352881f-86f1-80bb-92f3-fac3a3e10998" class="bulleted-list"><li style="list-style-type:circle">Notifies user of selection, warnings, and draw mode changes (“Drawing Mode Active”, “Duct Selected”, etc.).</li></ul></li></ul><ul id="2352881f-86f1-8005-80f6-eb7f85760c55" class="bulleted-list"><li style="list-style-type:disc"><strong>High contrast mode:</strong><ul id="2352881f-86f1-8059-8474-ded0dec87668" class="bulleted-list"><li style="list-style-type:circle">Orange, blue, and red states meet WCAG AA for contrast on light backgrounds.</li></ul></li></ul><hr id="2352881f-86f1-808a-9eaf-e5ed57af76e3"/><h2 id="2352881f-86f1-80c7-a06f-dae7c71f4930" class=""><strong>E. Resilience &amp; Error Handling</strong></h2><ul id="2352881f-86f1-806a-9de7-e687d7f56b94" class="bulleted-list"><li style="list-style-type:disc"><strong>No action is destructive by default</strong>—drawing, moving, or rotating can be undone/redone via history controls.</li></ul><ul id="2352881f-86f1-8002-bf50-d757b0f60895" class="bulleted-list"><li style="list-style-type:disc"><strong>Auto-save:</strong><ul id="2352881f-86f1-80c3-8612-d031a36b81f1" class="bulleted-list"><li style="list-style-type:circle">Canvas state is periodically saved; restoring from crash reloads last auto-saved model.</li></ul></li></ul><ul id="2352881f-86f1-805c-9dbb-e644730f65f8" class="bulleted-list"><li style="list-style-type:disc"><strong>Edge protection:</strong><ul id="2352881f-86f1-80a0-8c87-c97426439b9a" class="bulleted-list"><li style="list-style-type:circle">If a user drags or pans past model bounds, canvas gently “bounces” or fades to show end of workspace.</li></ul></li></ul><hr id="2352881f-86f1-8034-9220-e5732e6de680"/><h2 id="2352881f-86f1-80f7-b3c4-e396d0136408" class=""><strong>F. Rationale &amp; UX Justification</strong></h2><ul id="2352881f-86f1-804a-b6ff-c8ea0ac2e513" class="bulleted-list"><li style="list-style-type:disc"><strong>Direct, “what you see is what you get” (WYSIWYG)</strong> workspace encourages experimentation and minimizes user error.</li></ul><ul id="2352881f-86f1-8023-90f8-c3983808473d" class="bulleted-list"><li style="list-style-type:disc"><strong>Professional standards</strong> (orbit, pan, zoom, undo/redo) familiar to any engineer, designer, or estimator.</li></ul><ul id="2352881f-86f1-80d3-8150-e53236169e2b" class="bulleted-list"><li style="list-style-type:disc"><strong>Maximal visibility</strong>—by hiding all non-essential panels, user focus stays on modeling and reviewing system layout.</li></ul><hr id="2352881f-86f1-808a-82ce-f9376529cd03"/><h2 id="2352881f-86f1-8001-8178-d848b0d17f30" class=""><strong>G. Example Interaction Flow</strong></h2><ol type="1" id="2352881f-86f1-8007-8bb0-c50f66e47e6b" class="numbered-list" start="1"><li><strong>User opens project; canvas is empty or shows last saved state.</strong></li></ol><ol type="1" id="2352881f-86f1-80cc-92da-f7bc0e01a480" class="numbered-list" start="2"><li><strong>User pans with right-click, zooms with scroll, rotates view with left-click+drag.</strong></li></ol><ol type="1" id="2352881f-86f1-802c-9a57-d42f522cd3ba" class="numbered-list" start="3"><li><strong>User enables Pencil Tool (FAB): left-click places first node, continues drawing stick line for centerline.</strong></li></ol><ol type="1" id="2352881f-86f1-80e7-8eb4-e8284c1da16f" class="numbered-list" start="4"><li><strong>User double-clicks to finish run, presses Esc to cancel segment, or right-clicks to pan without leaving draw mode.</strong></li></ol><ol type="1" id="2352881f-86f1-80c8-b44c-f266f5e55047" class="numbered-list" start="5"><li><strong>User disables Pencil Tool, selects element(s), moves/edits as needed.</strong></li></ol><ol type="1" id="2352881f-86f1-80ad-babe-eebbea2b8682" class="numbered-list" start="6"><li><strong>User can rotate in 2D by holding Shift while dragging, useful for aligning to plans or elevations.</strong></li></ol><ol type="1" id="2352881f-86f1-80e1-a0d3-f9adafc05bc4" class="numbered-list" start="7"><li><strong>Any drawing or element with warnings glows as appropriate; hovering shows quick highlights.</strong></li></ol><hr id="2352881f-86f1-805e-bc74-fb67ee2ce044"/><h2 id="2352881f-86f1-80bf-8a8c-fac53c1a176f" class=""><strong>H. Table: Mouse/Key Interactions</strong></h2><table id="2352881f-86f1-80fe-9098-e4baa7ffb77f" class="simple-table"><thead class="simple-table-header"><tr id="2352881f-86f1-8009-b181-e3d64890e6c5"><th id="dxkx" class="simple-table-header-color simple-table-header">Action</th><th id="ZS|{" class="simple-table-header-color simple-table-header">Mouse/Key</th><th id="NCTh" class="simple-table-header-color simple-table-header">Result</th></tr></thead><tbody><tr id="2352881f-86f1-8034-bc9a-ebd742967b78"><td id="dxkx" class="">Pan view</td><td id="ZS|{" class="">Right-click + drag</td><td id="NCTh" class="">Moves viewport</td></tr><tr id="2352881f-86f1-80f3-8946-d53470c038dc"><td id="dxkx" class="">Zoom</td><td id="ZS|{" class="">Scroll/pinch</td><td id="NCTh" class="">In/out zoom centered on cursor</td></tr><tr id="2352881f-86f1-8054-9de7-da696bb06022"><td id="dxkx" class="">3D Rotate</td><td id="ZS|{" class="">Left-click + drag</td><td id="NCTh" class="">Rotates 3D view</td></tr><tr id="2352881f-86f1-80e0-b365-f039e6d16624"><td id="dxkx" class="">2D Rotate</td><td id="ZS|{" class="">Shift + Left-click+drag</td><td id="NCTh" class="">Restricts to horizontal/vertical</td></tr><tr id="2352881f-86f1-806e-a835-d0427844f737"><td id="dxkx" class="">Draw stick lines</td><td id="ZS|{" class="">Pencil ON + Left-click</td><td id="NCTh" class="">Places nodes for new duct runs</td></tr><tr id="2352881f-86f1-8031-82ba-c104bd32709b"><td id="dxkx" class="">End stick line</td><td id="ZS|{" class="">Pencil ON + Double-click</td><td id="NCTh" class="">Ends duct run</td></tr><tr id="2352881f-86f1-8088-b881-e6c713360dbd"><td id="dxkx" class="">Cancel drawing</td><td id="ZS|{" class="">Esc</td><td id="NCTh" class="">Exits drawing mode</td></tr><tr id="2352881f-86f1-804e-8ac8-e08fe5305f08"><td id="dxkx" class="">Select element</td><td id="ZS|{" class="">Pencil OFF + Left-click</td><td id="NCTh" class="">Selects duct/fitting/equipment</td></tr><tr id="2352881f-86f1-80f5-a73d-c82341b380cb"><td id="dxkx" class="">Multi-select</td><td id="ZS|{" class="">Ctrl/Cmd + Click</td><td id="NCTh" class="">Adds to selection</td></tr><tr id="2352881f-86f1-8084-91a2-eee331578477"><td id="dxkx" class="">Undo/Redo</td><td id="ZS|{" class="">Ctrl/Cmd+Z / Ctrl/Cmd+Y</td><td id="NCTh" class="">Undo/redo any change</td></tr></tbody></table><hr id="2352881f-86f1-803a-bca1-d5eec1e77075"/><h2 id="2352881f-86f1-80de-be2c-da81e5bc347e" class=""><strong>I. Theming &amp; Visuals</strong></h2><ul id="2352881f-86f1-8081-8595-deb2cec7b320" class="bulleted-list"><li style="list-style-type:disc"><strong>Background:</strong> #FAFBFC (white, very light)</li></ul><ul id="2352881f-86f1-80cd-bdb3-c37c7d6d8c33" class="bulleted-list"><li style="list-style-type:disc"><strong>Grid (optional):</strong> #E3E7EA, 15% opacity</li></ul><ul id="2352881f-86f1-8033-8cfd-d09b391a0769" class="bulleted-list"><li style="list-style-type:disc"><strong>Selection Outline:</strong> #FF9800 (orange)</li></ul><ul id="2352881f-86f1-805a-bf2d-f768b73ac8ad" class="bulleted-list"><li style="list-style-type:disc"><strong>Drawing Line:</strong> #FFA726 (orange, 80% opacity)</li></ul><ul id="2352881f-86f1-8028-a27a-d7e8fd9b92db" class="bulleted-list"><li style="list-style-type:disc"><strong>Hover:</strong> #00BCD4 (light blue, 40% opacity)</li></ul><ul id="2352881f-86f1-80bb-8292-f53a31940c2e" class="bulleted-list"><li style="list-style-type:disc"><strong>Warning:</strong> #FF5252 (red) / #FFEB3B (yellow) depending on severity</li></ul></details></li></ul><hr id="2332881f-86f1-801d-a17e-f95182e16acb"/><ul id="2332881f-86f1-8033-aabd-c64e63888942" class="toggle"><li><details open=""><summary><strong>3. Drawing Tool (Floating Action Button – FAB)</strong></summary><h1 id="2352881f-86f1-80a5-935f-f7dc24fdb6a8" class=""><strong>3. Drawing Tool FAB (Floating Action Button) — FINAL SPECIFICATION</strong></h1><hr id="2352881f-86f1-80da-a06d-eca09c1842e5"/><h2 id="2352881f-86f1-80df-8de1-ffebef635ac0" class=""><strong>A. Naming</strong></h2><ul id="2352881f-86f1-803b-83a4-d2691b3c1929" class="bulleted-list"><li style="list-style-type:disc"><strong>UI Short Name:</strong> Drawing Tool FAB</li></ul><ul id="2352881f-86f1-800b-bd9d-c36025299146" class="bulleted-list"><li style="list-style-type:disc"><strong>System Label:</strong> “Draw Duct Lines”</li></ul><ul id="2352881f-86f1-8066-99a4-eb1a0548d564" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong> Standard Pencil (aligned with international design and CAD conventions)</li></ul><ul id="2352881f-86f1-80b0-9afc-fbf3a31e8b66" class="bulleted-list"><li style="list-style-type:disc"><strong>State Names:</strong> OFF, ON, Drawing In Progress</li></ul><hr id="2352881f-86f1-8042-bf65-d73eca62df58"/><h2 id="2352881f-86f1-8038-80fa-d8e8719211ed" class=""><strong>B. Placement</strong></h2><ul id="2352881f-86f1-806b-b1f2-e95ab131c950" class="bulleted-list"><li style="list-style-type:disc"><strong>Viewport Location:</strong> Always floating at bottom-right corner of the 3D canvas.<ul id="2352881f-86f1-8091-b357-eda8cf0aff3a" class="bulleted-list"><li style="list-style-type:circle">Never obstructed by overlays or panels.</li></ul><ul id="2352881f-86f1-80d4-aa73-f663b89ed867" class="bulleted-list"><li style="list-style-type:circle">Smart repositioning for mobile/tablet or accessibility modes.</li></ul><ul id="2352881f-86f1-801a-8312-c1673e65b55a" class="bulleted-list"><li style="list-style-type:circle">Z-index above other floating controls (but beneath pop-up panels).</li></ul></li></ul><hr id="2352881f-86f1-8064-94a4-f923ccdbe174"/><h2 id="2352881f-86f1-80ce-8042-d9fcdb60ea10" class=""><strong>C. Behavior &amp; State Logic</strong></h2><h3 id="2352881f-86f1-80a8-b20b-c9d939358d52" class=""><strong>State 1: OFF</strong></h3><ul id="2352881f-86f1-802b-9120-ed67247dfbee" class="bulleted-list"><li style="list-style-type:disc"><strong>Visual:</strong><ul id="2352881f-86f1-800f-802f-fa8744ba0e29" class="bulleted-list"><li style="list-style-type:circle">FAB appears neutral grey (<code>#BDBDBD</code>), high-contrast icon, 100% opacity.</li></ul><ul id="2352881f-86f1-8049-9159-e9f6e0c66f0a" class="bulleted-list"><li style="list-style-type:circle">Tooltip: <strong>“Draw Duct Lines (OFF)”</strong></li></ul></li></ul><ul id="2352881f-86f1-8090-b9e6-f64d0d96e77e" class="bulleted-list"><li style="list-style-type:disc"><strong>Interaction:</strong><ul id="2352881f-86f1-804c-b05f-fabe759cfb1a" class="bulleted-list"><li style="list-style-type:circle">Left-click toggles ON (draw mode).</li></ul><ul id="2352881f-86f1-80ad-b03b-e11baa905e18" class="bulleted-list"><li style="list-style-type:circle">FAB can be toggled with keyboard shortcut <strong>D</strong> (documented in tooltip).</li></ul><ul id="2352881f-86f1-8089-8429-e7ee20eb9767" class="bulleted-list"><li style="list-style-type:circle">On canvas, all clicks are for selection (no drawing).</li></ul></li></ul><hr id="2352881f-86f1-8064-8993-c02acc064051"/><h3 id="2352881f-86f1-801a-82de-f5a9d12e80b6" class=""><strong>State 2: ON (Ready, Not Drawing)</strong></h3><ul id="2352881f-86f1-8042-97e0-e45ddd71b592" class="bulleted-list"><li style="list-style-type:disc"><strong>Visual:</strong><ul id="2352881f-86f1-8014-b58b-fdcca4a24dc6" class="bulleted-list"><li style="list-style-type:circle">FAB becomes orange (<code>#FF9800</code>), highly visible, 100% opacity.</li></ul><ul id="2352881f-86f1-809d-b8ea-c90b656190c1" class="bulleted-list"><li style="list-style-type:circle">Cursor changes to pencil/crosshair.</li></ul><ul id="2352881f-86f1-8093-b3cc-d7c94c0d7d4c" class="bulleted-list"><li style="list-style-type:circle">Tooltip: <strong>“Draw Duct Lines (ON)”</strong></li></ul></li></ul><ul id="2352881f-86f1-801b-9438-e4e92ef2b6cb" class="bulleted-list"><li style="list-style-type:disc"><strong>Interaction:</strong><ul id="2352881f-86f1-80b0-a072-e4d23e744f84" class="bulleted-list"><li style="list-style-type:circle">Left-click anywhere on canvas:<ul id="2352881f-86f1-80c1-b0f6-ec5f67dcd4da" class="bulleted-list"><li style="list-style-type:square"><strong>Immediately creates the first node.</strong></li></ul><ul id="2352881f-86f1-80ff-b32a-d43238b3bca7" class="bulleted-list"><li style="list-style-type:square"><strong>Immediately triggers a Pop-Up Property Panel (“Duct Properties”)</strong> with fields:<ul id="2352881f-86f1-8055-b7a1-e007ebfc503d" class="bulleted-list"><li style="list-style-type:disc"><strong>Width, Height, or Diameter:</strong> (user can select shape: rectangular or round)</li></ul><ul id="2352881f-86f1-80eb-9aac-c28307f94a4a" class="bulleted-list"><li style="list-style-type:disc"><strong>Material Type:</strong> (dropdown)</li></ul><ul id="2352881f-86f1-8032-9cf6-cc8e2b0ff86f" class="bulleted-list"><li style="list-style-type:disc"><strong>Insulation:</strong> (Y/N, thickness)</li></ul><ul id="2352881f-86f1-80cd-aff0-cdc7b9d0b6cc" class="bulleted-list"><li style="list-style-type:disc"><strong>Default Duct Name:</strong> (editable)</li></ul></li></ul><ul id="2352881f-86f1-80c1-b724-e97a1a8677db" class="bulleted-list"><li style="list-style-type:square">User sets/accepts duct properties for the current run.</li></ul><ul id="2352881f-86f1-80ec-8420-c86c88313abe" class="bulleted-list"><li style="list-style-type:square">Closing this pop-up (by confirming or hitting Enter) resumes drawing (with those properties pre-applied).</li></ul></li></ul><ul id="2352881f-86f1-80ce-84af-d206b429f1f4" class="bulleted-list"><li style="list-style-type:circle">Drawing stick line proceeds node-by-node as before (click = place node, double-click = end line/run).</li></ul><ul id="2352881f-86f1-800a-8d72-d5d3fd7b2a21" class="bulleted-list"><li style="list-style-type:circle"><strong>Right-click:</strong> Pan (even in draw mode).</li></ul><ul id="2352881f-86f1-80d3-a4f5-dd661e645887" class="bulleted-list"><li style="list-style-type:circle"><strong>Esc:</strong> Cancels the current line segment (returns to ready to start new line, ON state).</li></ul></li></ul><hr id="2352881f-86f1-8008-8e35-faf12ec3267b"/><h3 id="2352881f-86f1-80fc-849a-f01f9a381324" class=""><strong>State 3: Drawing in Progress</strong></h3><ul id="2352881f-86f1-8049-bf61-d0b2af475dc9" class="bulleted-list"><li style="list-style-type:disc"><strong>Visual:</strong><ul id="2352881f-86f1-803d-a931-d859814fd013" class="bulleted-list"><li style="list-style-type:circle">FAB maintains orange, but opacity reduced to <strong>60%</strong> (signals in-progress action).</li></ul><ul id="2352881f-86f1-80f2-89d1-e4de0b364ac5" class="bulleted-list"><li style="list-style-type:circle">Tooltip: <strong>“Drawing in progress…”</strong></li></ul></li></ul><ul id="2352881f-86f1-8094-ad6d-c21228f3a2b1" class="bulleted-list"><li style="list-style-type:disc"><strong>Interaction:</strong><ul id="2352881f-86f1-8095-89e6-debc53ef3269" class="bulleted-list"><li style="list-style-type:circle">Can click FAB to exit draw mode at any time (see next).</li></ul><ul id="2352881f-86f1-804a-8f78-c8946dee81cf" class="bulleted-list"><li style="list-style-type:circle">Drawing ends when user double-clicks, presses Esc, or toggles FAB OFF.</li></ul></li></ul><hr id="2352881f-86f1-80ee-8535-c723af50ea1c"/><h3 id="2352881f-86f1-80f8-bdbf-cff1c722614b" class=""><strong>Toggle OFF — Ending Draw Mode</strong></h3><ul id="2352881f-86f1-8052-8aab-ca19c40fe75a" class="bulleted-list"><li style="list-style-type:disc"><strong>Action:</strong><ul id="2352881f-86f1-801c-8290-f31fcf443178" class="bulleted-list"><li style="list-style-type:circle"><strong>Clicking the FAB</strong> (or pressing “D”) while ON <strong>auto-converts all in-progress lines</strong> to 3D ducts:<ul id="2352881f-86f1-809f-8015-c586cec3dde6" class="bulleted-list"><li style="list-style-type:square">Ducts extruded using the most recently entered properties (or properties assigned per run).</li></ul><ul id="2352881f-86f1-80f6-b48a-d7f17d5d3702" class="bulleted-list"><li style="list-style-type:square">Each segment inherits the properties assigned in its run.</li></ul><ul id="2352881f-86f1-808f-8f18-ca36cc5a4bcb" class="bulleted-list"><li style="list-style-type:square">Any disconnected or incomplete lines are validated; user is warned if conversion cannot complete (e.g., floating lines).</li></ul></li></ul><ul id="2352881f-86f1-80ab-9a4a-f9b26291528f" class="bulleted-list"><li style="list-style-type:circle">Tooltip: <strong>“Draw Duct Lines (OFF)”</strong></li></ul><ul id="2352881f-86f1-8089-a0f7-c065c4e6c2a8" class="bulleted-list"><li style="list-style-type:circle">FAB returns to grey/neutral visual.</li></ul></li></ul><hr id="2352881f-86f1-80b7-ae71-fcf790319982"/><h2 id="2352881f-86f1-8009-ad30-eb7168f6873d" class=""><strong>D. Pop-Up Property Panel (“Duct Properties”)</strong></h2><ul id="2352881f-86f1-80ab-a6f3-c50b1de8cde8" class="bulleted-list"><li style="list-style-type:disc"><strong>Trigger:</strong> Immediately upon placing the first node (start of a new run).</li></ul><ul id="2352881f-86f1-80dc-9453-ddafb00bd969" class="bulleted-list"><li style="list-style-type:disc"><strong>Fields:</strong><ul id="2352881f-86f1-8046-9959-d5a706bc637d" class="bulleted-list"><li style="list-style-type:circle"><strong>Duct Shape:</strong> (Rectangular, Round; radio select)</li></ul><ul id="2352881f-86f1-8049-bbbb-e1b2409517a2" class="bulleted-list"><li style="list-style-type:circle"><strong>Width, Height, Diameter:</strong> (based on shape)</li></ul><ul id="2352881f-86f1-8020-897f-d1e505c1dbf7" class="bulleted-list"><li style="list-style-type:circle"><strong>Material Type:</strong> (dropdown; e.g., Galv. Steel, Aluminum, Stainless)</li></ul><ul id="2352881f-86f1-8098-ad0c-c8aa3ca98927" class="bulleted-list"><li style="list-style-type:circle"><strong>Insulation:</strong> (toggle + numeric input for thickness)</li></ul><ul id="2352881f-86f1-80e9-8fd4-c86bce301e3b" class="bulleted-list"><li style="list-style-type:circle"><strong>Duct Name/Tag:</strong> (auto-increment; editable)</li></ul></li></ul><ul id="2352881f-86f1-80a1-ab58-e6cdc36eba5b" class="bulleted-list"><li style="list-style-type:disc"><strong>Behavior:</strong><ul id="2352881f-86f1-806b-9271-e884d452554b" class="bulleted-list"><li style="list-style-type:circle">Panel stays centered on screen or near click (space permitting).</li></ul><ul id="2352881f-86f1-80c6-a1a2-d5987a856535" class="bulleted-list"><li style="list-style-type:circle">Confirm applies values to current drawing run; cancels returns to drawing mode but aborts run.</li></ul><ul id="2352881f-86f1-8054-a544-e5e84896c371" class="bulleted-list"><li style="list-style-type:circle">Panel is fully keyboard navigable and accessible.</li></ul></li></ul><hr id="2352881f-86f1-80f5-9c35-c03b71ee8c7a"/><h2 id="2352881f-86f1-803f-91b1-e18f956d90ac" class=""><strong>E. Accessibility</strong></h2><ul id="2352881f-86f1-803f-892c-eae0db422c27" class="bulleted-list"><li style="list-style-type:disc"><strong>Keyboard:</strong><ul id="2352881f-86f1-8091-aae2-e19448919bf4" class="bulleted-list"><li style="list-style-type:circle">FAB is in tab order (can tab to, press Enter/Space to toggle).</li></ul><ul id="2352881f-86f1-8020-b0c2-d6754c4cde71" class="bulleted-list"><li style="list-style-type:circle">“D” key toggles draw mode ON/OFF.</li></ul><ul id="2352881f-86f1-80a6-88b9-c988c9abaaba" class="bulleted-list"><li style="list-style-type:circle">Esc cancels line in progress.</li></ul><ul id="2352881f-86f1-804e-912c-df7811007ced" class="bulleted-list"><li style="list-style-type:circle">Pop-Up panel is fully accessible (tab/arrow keys, Enter/Esc).</li></ul></li></ul><ul id="2352881f-86f1-80e3-a77a-f2db85ad70bf" class="bulleted-list"><li style="list-style-type:disc"><strong>Screen Reader:</strong><ul id="2352881f-86f1-80c9-97d2-d2b4f3130aa5" class="bulleted-list"><li style="list-style-type:circle">ARIA label always reflects state: “Draw Duct Lines, OFF/ON/Drawing in progress…”</li></ul></li></ul><ul id="2352881f-86f1-80cf-92f9-c501ec37ac80" class="bulleted-list"><li style="list-style-type:disc"><strong>Color Contrast:</strong><ul id="2352881f-86f1-80d2-9fd3-e12365388e66" class="bulleted-list"><li style="list-style-type:circle">Orange (<code>#FF9800</code>) and grey (<code>#BDBDBD</code>) meet/exceed WCAG 2.1 AA for icons and backgrounds.</li></ul></li></ul><ul id="2352881f-86f1-805c-986f-ee73c3f65b8b" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip:</strong><ul id="2352881f-86f1-803d-889e-e9eccde0014d" class="bulleted-list"><li style="list-style-type:circle">Always visible on hover/focus, with dynamic state message.</li></ul></li></ul><ul id="2352881f-86f1-80f4-8662-c22d9787b871" class="bulleted-list"><li style="list-style-type:disc"><strong>Placement:</strong><ul id="2352881f-86f1-8079-86be-eb1be2a5929a" class="bulleted-list"><li style="list-style-type:circle">FAB never obstructed, repositionable for accessibility needs.</li></ul></li></ul><hr id="2352881f-86f1-806c-8805-d56cefc37cbc"/><h2 id="2352881f-86f1-8006-a85c-e2185ab42bcb" class=""><strong>F. Visual &amp; Interaction Feedback</strong></h2><ul id="2352881f-86f1-80c1-94f1-d9e813a04d07" class="bulleted-list"><li style="list-style-type:disc"><strong>Opacity:</strong> FAB dims to signal in-progress drawing.</li></ul><ul id="2352881f-86f1-8074-a2b8-c3e9eb350491" class="bulleted-list"><li style="list-style-type:disc"><strong>Cursor:</strong> Pencil/crosshair when ON; pointer when OFF.</li></ul><ul id="2352881f-86f1-8080-b582-d62131cff2ee" class="bulleted-list"><li style="list-style-type:disc"><strong>Transition:</strong> FAB color/opacity transitions animated (≤150ms).</li></ul><ul id="2352881f-86f1-8088-85db-ef33e4c2a561" class="bulleted-list"><li style="list-style-type:disc"><strong>Pop-Up Panel:</strong> Zoom/fade-in animation; dismisses with Enter/Esc.</li></ul><ul id="2352881f-86f1-802c-89b2-ffc901170346" class="bulleted-list"><li style="list-style-type:disc"><strong>Auto-conversion:</strong> All drawn lines extruded instantly on mode exit (no “convert” button needed).</li></ul><hr id="2352881f-86f1-80a4-999e-cb331202c3ad"/><h2 id="2352881f-86f1-80a8-9f6f-ef0e4535e4ae" class=""><strong>G. Rationale &amp; Engineering Practice Alignment</strong></h2><ul id="2352881f-86f1-80c8-bb39-d670fb1cb73d" class="bulleted-list"><li style="list-style-type:disc"><strong>Efficiency:</strong> Immediate property input removes extra steps and prevents error-prone generic line drawing.</li></ul><ul id="2352881f-86f1-807f-a42f-daf555883ab3" class="bulleted-list"><li style="list-style-type:disc"><strong>Professional Standard:</strong> Mirrors Revit, CAD, and leading BIM tools: drawing mode always starts with property context.</li></ul><ul id="2352881f-86f1-80c0-8176-e688192f6bb7" class="bulleted-list"><li style="list-style-type:disc"><strong>Clarity:</strong> FAB always reflects state (color, tooltip, opacity) for user confidence and safety.</li></ul><ul id="2352881f-86f1-80cf-b416-c41ee4633d5d" class="bulleted-list"><li style="list-style-type:disc"><strong>Speed:</strong> Auto-conversion on toggle OFF allows engineers to draw, edit, and iterate in a rapid, natural workflow.</li></ul><ul id="2352881f-86f1-80e1-a74b-debae8990489" class="bulleted-list"><li style="list-style-type:disc"><strong>Accessibility:</strong> No mouse required for any core interaction.</li></ul><hr id="2352881f-86f1-8007-baed-ffe629b4cb4b"/><p id="2352881f-86f1-8035-855c-e433606f7c53" class=""><strong>This is the canonical spec for the Drawing Tool FAB for Air Duct Sizer 3D Tool MVP. All logic, UI, interaction, accessibility, and engineering rationale is covered.</strong></p></details></li></ul><hr id="2332881f-86f1-8091-884d-f9d0cdb65eb8"/><ul id="2332881f-86f1-803d-bb7d-d308f8abf0c6" class="toggle"><li><details open=""><summary><strong>4. Convert to 3D Button</strong></summary><h3 id="2332881f-86f1-80fa-874d-d2dedd3a74c1" class=""><strong>Purpose</strong></h3><p id="2332881f-86f1-80ea-a5e2-c363f47665a3" class="">Transforms <strong>all drawn stick lines</strong> (duct centerlines) into <strong>actual 3D duct models</strong> with default dimensions and fittings.</p><h3 id="2332881f-86f1-800d-90de-e2c6f8dcffd4" class=""><strong>Better Naming Suggestions</strong></h3><ol type="1" id="2332881f-86f1-8050-b2ce-e01cefdc3d1c" class="numbered-list" start="1"><li><strong>Generate Duct Model</strong> (clear for engineers)</li></ol><ol type="1" id="2332881f-86f1-800b-b4ab-d1ee1d4e34dc" class="numbered-list" start="2"><li><strong>Convert to 3D Ducts</strong> (explicit, user-friendly)</li></ol><ol type="1" id="2332881f-86f1-808f-8d3d-c7e7b978e11f" class="numbered-list" start="3"><li><strong>Build Ductwork</strong> (natural-language, workflow-oriented – <strong>recommended</strong>)</li></ol><ol type="1" id="2332881f-86f1-804c-88cf-c73d80ceb8e2" class="numbered-list" start="4"><li><strong>Extrude Duct Lines</strong> (technical, CAD-like)</li></ol><h3 id="2332881f-86f1-80cb-b938-cf158b7dfda4" class=""><strong>Specifications</strong></h3><ul id="2332881f-86f1-8010-8621-ec2ec3a244b6" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Large primary button (persistent, floating or placed in Calculation Bar — suggestion below)</li></ul><ul id="2332881f-86f1-8012-b966-c8bddab9423f" class="bulleted-list"><li style="list-style-type:disc"><strong>Placement (Recommended):</strong> <strong>Left of the FAB</strong> (bottom-right cluster) OR <strong>in the Calculation Bar</strong> for workflow consistency</li></ul><ul id="2332881f-86f1-8064-aeb8-c9574c6ca5fa" class="bulleted-list"><li style="list-style-type:disc"><strong>Behavior:</strong><ul id="2332881f-86f1-80cc-9a69-c55a9e7a1f89" class="bulleted-list"><li style="list-style-type:circle"><strong>On Click:</strong><ul id="2332881f-86f1-80f1-95dc-f28d5ccbeece" class="bulleted-list"><li style="list-style-type:square">Processes all connected stick lines into 3D extruded duct sections (based on default or user-specified dimensions)</li></ul><ul id="2332881f-86f1-805f-9531-d6e36ec9c4e4" class="bulleted-list"><li style="list-style-type:square">Auto-detects intersections and inserts default fittings (e.g., elbows, tees).</li></ul></li></ul><ul id="2332881f-86f1-807c-8d58-ed0ff1f6181f" class="bulleted-list"><li style="list-style-type:circle"><strong>Progress Indicator:</strong><ul id="2332881f-86f1-807a-92a0-f3b7de2907a3" class="bulleted-list"><li style="list-style-type:square">Shows a spinning loader over the button or temporary “Generating…” toast</li></ul></li></ul><ul id="2332881f-86f1-80f0-b9e2-cded7479d3bf" class="bulleted-list"><li style="list-style-type:circle"><strong>Undo Option:</strong><ul id="2332881f-86f1-80ac-a05d-edfe84a27c40" class="bulleted-list"><li style="list-style-type:square">After generation, shows <strong>“Undo Conversion”</strong> as a secondary action for 10 seconds (or until a new change is made).</li></ul></li></ul></li></ul></details></li></ul><hr id="2332881f-86f1-80a2-95ab-d99e8e180325"/><ul id="2332881f-86f1-80ca-873f-eaac36da9453" class="toggle"><li><details open=""><summary><strong>5. Context Property Panel </strong></summary><h3 id="2342881f-86f1-80a3-8db5-c2c06dd50071" class=""><strong>Context Property Panel — Air Duct Sizer 3D Tool</strong></h3><ul id="2342881f-86f1-8084-9c6f-c8f5d560ef1f" class="toggle"><li><details open=""><summary><strong>1. Name &amp; Role</strong></summary><ul id="2342881f-86f1-80dc-a0e3-e5c949212df3" class="bulleted-list"><li style="list-style-type:disc">Official Name: Context Property Panel</li></ul><ul id="2342881f-86f1-80fe-88e4-f8172f763f3d" class="bulleted-list"><li style="list-style-type:disc">Internal Reference: ContextPropertyPanel</li></ul><ul id="2342881f-86f1-80ae-9cc2-d01ea8d2c7bc" class="bulleted-list"><li style="list-style-type:disc">Purpose:<br/>An interactive, floating UI panel that appears when the user selects any element(s) (duct, fitting, room, equipment, or multi-select group) within the 3D canvas. It provides immediate access to key actions, editable properties, and element-specific status in a context-driven, visually modern interface.<br/></li></ul></details></li></ul><ul id="2342881f-86f1-808c-8a75-e786f6aa6ad1" class="toggle"><li><details open=""><summary><strong>2. Trigger &amp; Lifecycle</strong></summary><ul id="2342881f-86f1-8023-bb63-d44f694cfd3e" class="bulleted-list"><li style="list-style-type:disc">Display Trigger:<ul id="2342881f-86f1-806d-9100-fa6bec644ec4" class="bulleted-list"><li style="list-style-type:circle">Panel appears whenever one or more selectable elements are highlighted (clicked or multi-selected) in the 3D canvas.</li></ul></li></ul><ul id="2342881f-86f1-8018-9e60-e0ae4b6412e2" class="bulleted-list"><li style="list-style-type:disc">Dismissal:<ul id="2342881f-86f1-80e0-a9e3-f4f582a3bbd5" class="bulleted-list"><li style="list-style-type:circle">Panel disappears (“zooms out”) when the user:<ul id="2342881f-86f1-802e-b330-f8115ac4a7c8" class="bulleted-list"><li style="list-style-type:square">Deselects all elements,</li></ul><ul id="2342881f-86f1-80a8-9d19-c47639bd0a06" class="bulleted-list"><li style="list-style-type:square">Clicks outside both the panel and the selection,</li></ul><ul id="2342881f-86f1-80c4-8640-ea356c434e8f" class="bulleted-list"><li style="list-style-type:square">Presses Esc (keyboard).</li></ul></li></ul></li></ul><ul id="2342881f-86f1-801d-ba65-ceea1d72710c" class="bulleted-list"><li style="list-style-type:disc">Persistence:<ul id="2342881f-86f1-8029-b814-c07c8a4f24a5" class="bulleted-list"><li style="list-style-type:circle">Panel remains visible and interactive as long as the selection exists, regardless of panning/zooming the view.</li></ul></li></ul></details></li></ul><ul id="2342881f-86f1-80e2-b2f3-f198202f3097" class="toggle"><li><details open=""><summary><strong>3. Placement &amp; Movement</strong></summary><ul id="2342881f-86f1-8004-a218-d07acd9a0fab" class="bulleted-list"><li style="list-style-type:disc">Initial Placement:<ul id="2342881f-86f1-8020-9e2c-ed23ee87cb56" class="bulleted-list"><li style="list-style-type:circle">Panel dynamically appears in the largest available open space nearest to the first selected element (centroid for multi-select).</li></ul><ul id="2342881f-86f1-803f-a666-c0502e599b68" class="bulleted-list"><li style="list-style-type:circle">Never overlaps the selected geometry or obstructs critical UI.</li></ul><ul id="2342881f-86f1-800c-b014-d89533f835f1" class="bulleted-list"><li style="list-style-type:circle">Automatically repositions if window is resized and would otherwise occlude a selection.</li></ul></li></ul><ul id="2342881f-86f1-80a9-81ca-f435a98ca9db" class="bulleted-list"><li style="list-style-type:disc">Movability:<ul id="2342881f-86f1-8094-9696-ceba1f51ca08" class="bulleted-list"><li style="list-style-type:circle">User can move the panel anywhere within the viewport by clicking and dragging on any part of the panel (not just a header/grip).</li></ul></li></ul><ul id="2342881f-86f1-80b7-8e05-efae70f716ee" class="bulleted-list"><li style="list-style-type:disc">Resizing:<ul id="2342881f-86f1-8058-ab6b-f7a4422296a8" class="bulleted-list"><li style="list-style-type:circle">Four resize handles (standard corner grabbers) are displayed on panel hover, one at each corner.</li></ul><ul id="2342881f-86f1-807c-9211-c345766676ae" class="bulleted-list"><li style="list-style-type:circle">Panel can only be resized by dragging a corner handle—edges and borders do not respond.</li></ul><ul id="2342881f-86f1-806c-9c9a-e2254d26e039" class="bulleted-list"><li style="list-style-type:circle">Minimum and maximum sizes enforced to preserve usability.</li></ul></li></ul></details></li></ul><ul id="2342881f-86f1-8034-a3fc-c685b69766bf" class="toggle"><li><details open=""><summary><strong>4. Animation</strong></summary><ul id="2342881f-86f1-804d-8629-f5baa549c8ed" class="bulleted-list"><li style="list-style-type:disc">Panel Appearance:<ul id="2342881f-86f1-805b-96fc-df5ba482493d" class="bulleted-list"><li style="list-style-type:circle">Zooms in: Panel animates from the first selected element—scaling and fading in (e.g., scale from 0.6 to 1.0, opacity 0.6 to 1.0) to its calculated position in the viewport.</li></ul></li></ul><ul id="2342881f-86f1-8043-b534-d235609feb57" class="bulleted-list"><li style="list-style-type:disc">Panel Dismissal:<ul id="2342881f-86f1-80ab-9d28-c878a00a3cd2" class="bulleted-list"><li style="list-style-type:circle">Zooms out: On dismissal, panel animates back toward the first selected element, scaling down and fading to opacity 0.</li></ul></li></ul><ul id="2342881f-86f1-80ee-8104-d1e96c59c3a3" class="bulleted-list"><li style="list-style-type:disc">Resize Handle Animation:<ul id="2342881f-86f1-80cc-9d7b-eed1b10a15c3" class="bulleted-list"><li style="list-style-type:circle">On hover, each corner handle animates in place (e.g., expands, glows, or gains offset/parallel marks) to signal interactivity.</li></ul></li></ul></details></li></ul><ul id="2342881f-86f1-8078-8f2d-f19fd299804d" class="toggle"><li><details open=""><summary><strong>5. Visual Design</strong></summary><ul id="2342881f-86f1-80d7-ad1e-c03d096d61fd" class="bulleted-list"><li style="list-style-type:disc">Background:<ul id="2342881f-86f1-805a-bd9b-c580a82eafa6" class="bulleted-list"><li style="list-style-type:circle">Frosted Glass:<ul id="2342881f-86f1-809c-a8e1-d527d77c4af4" class="bulleted-list"><li style="list-style-type:square">Semi-transparent white, high blur (e.g., rgba(255,255,255,0.55), 24px backdrop blur).</li></ul><ul id="2342881f-86f1-80c6-a7bd-fb69f74029bf" class="bulleted-list"><li style="list-style-type:square">Color and effect remain the same in light and dark modes (panel and canvas do not invert or change).</li></ul></li></ul></li></ul><ul id="2342881f-86f1-8067-8614-dcc609aeab56" class="bulleted-list"><li style="list-style-type:disc">Border:<ul id="2342881f-86f1-8091-9027-f5acd7ac6bda" class="bulleted-list"><li style="list-style-type:circle">Liquid Glass:<ul id="2342881f-86f1-80f4-82f2-f2c8ec7f64b6" class="bulleted-list"><li style="list-style-type:square">Semi-transparent white or icy-blue, smooth glowing effect, visually “liquid” (not flat/solid).</li></ul><ul id="2342881f-86f1-80a0-aaf8-de9ef528ef39" class="bulleted-list"><li style="list-style-type:square">Thickness: ~2px; color consistent across all themes.</li></ul></li></ul></li></ul><ul id="2342881f-86f1-80b2-a57d-cc0557ac1443" class="bulleted-list"><li style="list-style-type:disc">Drop Shadow:<ul id="2342881f-86f1-801c-89ea-e15e82a638c5" class="bulleted-list"><li style="list-style-type:circle">Soft white or subtle blue outer glow, low opacity, for depth without distraction.</li></ul></li></ul><ul id="2342881f-86f1-807e-ae6e-f57a0ba66082" class="bulleted-list"><li style="list-style-type:disc">Resize Handles:<ul id="2342881f-86f1-805a-a527-e8ea64c71c93" class="bulleted-list"><li style="list-style-type:circle">Four circular grabbers at the corners, semi-translucent, glowing on hover.</li></ul><ul id="2342881f-86f1-8051-b692-c8a58ba07f1e" class="bulleted-list"><li style="list-style-type:circle">On hover, each handle may animate (e.g., pulse or gain double-parallel offset marks for visibility).</li></ul></li></ul><ul id="2342881f-86f1-80de-9bec-f3c962a1a248" class="bulleted-list"><li style="list-style-type:disc">Hover State (Panel):<ul id="2342881f-86f1-8022-8663-c0b8caabd645" class="bulleted-list"><li style="list-style-type:circle">Entire panel receives a faint white/blue glow on hover, enhancing discoverability.</li></ul></li></ul></details></li></ul><ul id="2342881f-86f1-801b-b74a-c3aaf5c0a979" class="toggle"><li><details open=""><summary><strong>6. Contents &amp; Layout</strong></summary><ul id="2342881f-86f1-8079-8b4c-ca23eeb9f085" class="bulleted-list"><li style="list-style-type:disc">Panel Orientation:<ul id="2342881f-86f1-80f7-9845-f5e3c03a18e7" class="bulleted-list"><li style="list-style-type:circle">Horizontal bar, arranged as follows for both single and multiple selection:<ol type="1" id="2342881f-86f1-80fd-b603-da885191b941" class="numbered-list" start="1"><li>Quick Actions<ul id="2342881f-86f1-8041-98d5-fc64b6940335" class="bulleted-list"><li style="list-style-type:disc">Horizontally grouped at the end of the panel closest to the selected element(s).</li></ul><ul id="2342881f-86f1-8045-aa9d-eeef98615895" class="bulleted-list"><li style="list-style-type:disc">Always includes:<ul id="2342881f-86f1-8000-b533-dea5ea1f0671" class="bulleted-list"><li style="list-style-type:circle">Add (contextual: Add Branch, Add Equipment, etc.)</li></ul><ul id="2342881f-86f1-803e-be0c-f222c9906742" class="bulleted-list"><li style="list-style-type:circle">Remove (removes current selection)</li></ul></li></ul><ul id="2342881f-86f1-8005-91c3-d930407634aa" class="bulleted-list"><li style="list-style-type:disc">No “edit” action—editing is always direct via property fields.</li></ul></li></ol><ol type="1" id="2342881f-86f1-8065-97b8-ccf3f1b29d69" class="numbered-list" start="2"><li>Editable Properties<ul id="2342881f-86f1-8027-a486-e20a29c56f6a" class="bulleted-list"><li style="list-style-type:disc">Centered within the panel, occupying most of the horizontal space.</li></ul><ul id="2342881f-86f1-80b5-a5eb-f075df8efc1c" class="bulleted-list"><li style="list-style-type:disc">Displays all modifiable fields for the selected element(s):<ul id="2342881f-86f1-8044-ab8a-fd8e63e9a781" class="bulleted-list"><li style="list-style-type:circle">For multi-select, shows only shared properties; unique properties shown as “(varied)” or disabled.</li></ul><ul id="2342881f-86f1-8011-aa8e-c63cbb3619c4" class="bulleted-list"><li style="list-style-type:circle">Examples:<ul id="2342881f-86f1-8053-afca-ddc949a8ac87" class="bulleted-list"><li style="list-style-type:square">Duct: size, material, length, flow</li></ul><ul id="2342881f-86f1-8010-9b05-eef372a78949" class="bulleted-list"><li style="list-style-type:square">Room: name, type, target flow</li></ul><ul id="2342881f-86f1-80f4-b62b-e83bab7848e7" class="bulleted-list"><li style="list-style-type:square">Equipment: type, capacity</li></ul></li></ul></li></ul><ul id="2342881f-86f1-80e0-975a-f8bd9ef671bc" class="bulleted-list"><li style="list-style-type:disc">All fields have liquid glass background (slightly higher opacity frosted glass), with unit hints and always-labeled.</li></ul><ul id="2342881f-86f1-8015-ae08-fb8e85e42aae" class="bulleted-list"><li style="list-style-type:disc">Field Hover/Focus: Fields brighten or glow gently on hover/focus, with no harsh contrast.</li></ul></li></ol><ol type="1" id="2342881f-86f1-80a0-b7d3-fd0f74ec091e" class="numbered-list" start="3"><li>Status Section<ul id="2342881f-86f1-801b-a96f-c65c812b0f95" class="bulleted-list"><li style="list-style-type:disc">At the far end of the panel, furthest from selection.</li></ul><ul id="2342881f-86f1-8069-a848-ea69f6bd4f17" class="bulleted-list"><li style="list-style-type:disc">Contains:<ul id="2342881f-86f1-8011-b6c4-fb3e555735d5" class="bulleted-list"><li style="list-style-type:circle">Warning/error badges (color-coded: red, yellow, green)</li></ul><ul id="2342881f-86f1-803b-9dc0-f3507c2d5d12" class="bulleted-list"><li style="list-style-type:circle">Lock indicators (if property is read-only or tier-locked)</li></ul><ul id="2342881f-86f1-8004-800b-db2fad25b591" class="bulleted-list"><li style="list-style-type:circle">Selection summary (for multi-select: total length, combined flow, etc.)</li></ul><ul id="2342881f-86f1-801e-9c24-ea11d99b7acd" class="bulleted-list"><li style="list-style-type:circle">Info tooltips linked to code/standard references (e.g., SMACNA, ASHRAE)</li></ul></li></ul></li></ol></li></ul></li></ul></details></li></ul><ul id="2342881f-86f1-80c2-a770-d6aac9a56fe8" class="toggle"><li><details open=""><summary><strong>7. Accessibility</strong></summary><ul id="2342881f-86f1-8040-a501-f673b0b1bfbf" class="bulleted-list"><li style="list-style-type:disc">Keyboard Navigation:<ul id="2342881f-86f1-80e5-b631-c327a2051c3e" class="bulleted-list"><li style="list-style-type:circle">Full tab/arrow key support for quick actions, properties, and status section.</li></ul><ul id="2342881f-86f1-801f-bc47-e41687907c38" class="bulleted-list"><li style="list-style-type:circle">ESC closes the panel and deselects.</li></ul><ul id="2342881f-86f1-803a-a1c4-c51ad1721f59" class="bulleted-list"><li style="list-style-type:circle">Resize handles are focusable via tab and adjustable with keyboard (arrow keys when focused).</li></ul><ul id="2342881f-86f1-8014-bd25-e728474c067a" class="bulleted-list"><li style="list-style-type:circle">Panel movement can be triggered by keyboard shortcut (e.g., Alt+Arrow).</li></ul></li></ul><ul id="2342881f-86f1-8084-90b0-f8e9ce75c380" class="bulleted-list"><li style="list-style-type:disc">Screen Reader Support:<ul id="2342881f-86f1-8055-9586-cd7a9e3ea2bd" class="bulleted-list"><li style="list-style-type:circle">Panel announces itself as: “Context Property Panel for [element type] selected.”</li></ul><ul id="2342881f-86f1-8060-a506-f78e392de933" class="bulleted-list"><li style="list-style-type:circle">Each action, field, and badge fully described.</li></ul><ul id="2342881f-86f1-806a-9000-f5d1e5bf87d0" class="bulleted-list"><li style="list-style-type:circle">Announces field state changes (“Size: 12x8 inches, required field, warning: velocity exceeds limit.”)</li></ul></li></ul><ul id="2342881f-86f1-804f-b01a-e37df304ba5a" class="bulleted-list"><li style="list-style-type:disc">Contrast &amp; Visibility:<ul id="2342881f-86f1-80ea-995f-e092a1bbe79d" class="bulleted-list"><li style="list-style-type:circle">Text, icons, fields, and handles always meet or exceed WCAG 2.1 AA.</li></ul><ul id="2342881f-86f1-80e9-ac80-c7bad7290eef" class="bulleted-list"><li style="list-style-type:circle">Frosted and liquid glass never reduce essential contrast or legibility.</li></ul></li></ul></details></li></ul><ul id="2342881f-86f1-8029-a5ad-df0f6b384b8a" class="toggle"><li><details open=""><summary><strong>8. Rationale</strong></summary><ul id="2342881f-86f1-8069-b324-ece622c7c647" class="bulleted-list"><li style="list-style-type:disc">Modern Professionalism:<ul id="2342881f-86f1-80ae-96fe-df8b7c5d9519" class="bulleted-list"><li style="list-style-type:circle">The frosted glass and liquid glass border provide a contemporary, high-end visual, in line with top-tier engineering software.</li></ul></li></ul><ul id="2342881f-86f1-80db-88e3-d2b743ac7931" class="bulleted-list"><li style="list-style-type:disc">Contextual Workflow:<ul id="2342881f-86f1-8072-83de-ef2e8ae57758" class="bulleted-list"><li style="list-style-type:circle">Panel placement and animation reinforce focus on selected elements, boosting user orientation and reducing cognitive load.</li></ul></li></ul><ul id="2342881f-86f1-8030-ba13-e490287c3694" class="bulleted-list"><li style="list-style-type:disc">Direct Editing:<ul id="2342881f-86f1-80ee-800d-c1c95f05f7ac" class="bulleted-list"><li style="list-style-type:circle">Removing the edit action streamlines interaction—users work directly with property fields, with instant feedback.</li></ul></li></ul><ul id="2342881f-86f1-8009-b23c-f1812062e6c9" class="bulleted-list"><li style="list-style-type:disc">Discoverability:<ul id="2342881f-86f1-8075-bbc6-f98b834b3021" class="bulleted-list"><li style="list-style-type:circle">Movability and resize handles are clear, visually prominent, and universally understood by professional users.</li></ul></li></ul><ul id="2342881f-86f1-801f-97cc-e3e244d03819" class="bulleted-list"><li style="list-style-type:disc">Single Source of Interaction:<ul id="2342881f-86f1-8042-8fbe-e08110d88c73" class="bulleted-list"><li style="list-style-type:circle">Only one Context Property Panel is ever shown at a time, even for multi-select—keeps the workspace clean.</li></ul></li></ul></details></li></ul><ul id="2342881f-86f1-800f-9024-fe5c8642fc78" class="toggle"><li><details open=""><summary><strong>9. Edge Cases &amp; Constraints</strong></summary><ul id="2342881f-86f1-80fe-b7e0-ed3b836f2fa4" class="bulleted-list"><li style="list-style-type:disc">Panel cannot be resized by edge or border—only by visible corner handles.</li></ul><ul id="2342881f-86f1-80c6-b4af-e6890fed0466" class="bulleted-list"><li style="list-style-type:disc">Panel never covers the selected element or blocks essential UI (smart placement logic).</li></ul><ul id="2342881f-86f1-8055-af30-cf9eb458e4e1" class="bulleted-list"><li style="list-style-type:disc">Multi-select always shows the same Context Property Panel; non-shared fields are shown as “(varied)” or disabled, not hidden.</li></ul><ul id="2342881f-86f1-8077-9e19-e6783843a3df" class="bulleted-list"><li style="list-style-type:disc">Panel does not collapse or auto-move during canvas pan/zoom—remains until user acts.</li></ul></details></li></ul><ul id="2342881f-86f1-80f1-a1af-d1bd9e25935a" class="toggle"><li><details open=""><summary><strong>10. Summary Table</strong></summary><table id="2342881f-86f1-8078-bee3-f693d3b4f837" class="simple-table"><tbody><tr id="2342881f-86f1-80c4-b838-d068535ed644"><td id="mLdi" class=""><strong>Attribute</strong></td><td id="oKtQ" class=""><strong>Description</strong></td></tr><tr id="2342881f-86f1-809a-b349-e5123e7a28ea"><td id="mLdi" class="">Name</td><td id="oKtQ" class="">Context Property Panel</td></tr><tr id="2342881f-86f1-802d-861b-dfb40beec8bc"><td id="mLdi" class="">Trigger</td><td id="oKtQ" class="">Selection of any element(s) in 3D canvas</td></tr><tr id="2342881f-86f1-80b9-b1d7-f7244e2eacee"><td id="mLdi" class="">Placement</td><td id="oKtQ" class="">Smart float, nearest open space to first selection; user can move</td></tr><tr id="2342881f-86f1-801f-bb8c-f4fb07943657"><td id="mLdi" class="">Animation</td><td id="oKtQ" class="">Zoom in from/to first selection (panel); resize handles animate on hover</td></tr><tr id="2342881f-86f1-8050-91c0-d3447b5d9280"><td id="mLdi" class="">Background</td><td id="oKtQ" class="">Frosted glass (same for light/dark), semi-transparent, blurred</td></tr><tr id="2342881f-86f1-8051-99fc-fc78a105d172"><td id="mLdi" class="">Border</td><td id="oKtQ" class="">Liquid glass (white/icy-blue glow), consistent across themes</td></tr><tr id="2342881f-86f1-8058-a5f9-e7cf0e82b001"><td id="mLdi" class="">Actions</td><td id="oKtQ" class="">Add (contextual), Remove</td></tr><tr id="2342881f-86f1-8072-9fd8-f05e74f2f9ea"><td id="mLdi" class="">Editable Fields</td><td id="oKtQ" class="">Centered, liquid glass input style, clear labels, shared fields for multi-select</td></tr><tr id="2342881f-86f1-8053-9e66-dfdbd574a285"><td id="mLdi" class="">Status Section</td><td id="oKtQ" class="">Warnings, lock, summary, info tooltips</td></tr><tr id="2342881f-86f1-8054-bdf1-daec9ac9764e"><td id="mLdi" class="">Resize</td><td id="oKtQ" class="">Corner handles only, visible on hover, keyboard and mouse accessible</td></tr><tr id="2342881f-86f1-80fa-b3ac-deca9cc7452e"><td id="mLdi" class="">Accessibility</td><td id="oKtQ" class="">Full tab/arrow nav, ARIA labeling, contrast AA, move/resize by keyboard and mouse</td></tr><tr id="2342881f-86f1-8064-90b2-d50ea2a7724c"><td id="mLdi" class="">Persistence</td><td id="oKtQ" class="">Never auto-hides, only user-dismissed, always one panel</td></tr><tr id="2342881f-86f1-80b1-bd51-c38d5ad08e89"><td id="mLdi" class="">Rationale</td><td id="oKtQ" class="">Professional, context-driven, clutter-free, matches modern engineering/CAD UI paradigms</td></tr></tbody></table></details></li></ul><ul id="2342881f-86f1-8033-9c41-fdb55b811f7f" class="toggle"><li><details open=""><summary><strong>11. Context Property Panel — Color Codes</strong></summary><hr id="2342881f-86f1-809c-8cbe-f1584cdf4dd7"/><h2 id="2342881f-86f1-8017-a2d8-c077c905a279" class=""><strong>Context Property Panel — Color Codes Specification</strong></h2><hr id="2342881f-86f1-80d1-aaa8-fe5705342e43"/><h3 id="2342881f-86f1-8087-8738-f4e8793c5d47" class=""><strong>Panel Background (Frosted Glass)</strong></h3><ul id="2342881f-86f1-809b-a2fc-e654e1d4523e" class="bulleted-list"><li style="list-style-type:disc"><strong>Primary Panel:</strong><ul id="2342881f-86f1-80ef-bea7-f62c8bb1620f" class="bulleted-list"><li style="list-style-type:circle"><code>background: rgba(247, 250, 255, 0.55)</code><p id="2342881f-86f1-8097-aa64-e80204976df8" class=""><em>(Very light, clean white-blue; soft frost. Ensures contrast on both light/dark canvas.)</em></p></li></ul><ul id="2342881f-86f1-80e7-a783-cfc848700da5" class="bulleted-list"><li style="list-style-type:circle"><strong>HEX Preview:</strong> <code>#F7FAFF</code> at 55% opacity</li></ul></li></ul><ul id="2342881f-86f1-80c4-b657-e88587d5314f" class="bulleted-list"><li style="list-style-type:disc"><strong>Backdrop Blur:</strong><ul id="2342881f-86f1-80b5-af05-ef4389dd9d9b" class="bulleted-list"><li style="list-style-type:circle"><em>CSS</em>: <code>backdrop-filter: blur(24px);</code></li></ul></li></ul><hr id="2342881f-86f1-8076-9ea2-c4f5612dde63"/><h3 id="2342881f-86f1-809e-afc0-ea807453511d" class=""><strong>Panel Border (Liquid Glass)</strong></h3><ul id="2342881f-86f1-800e-be89-dd4b6b593265" class="bulleted-list"><li style="list-style-type:disc"><strong>Color:</strong><ul id="2342881f-86f1-80c3-a897-c78c68acceec" class="bulleted-list"><li style="list-style-type:circle"><code>border: 2px solid rgba(168, 212, 255, 0.70)</code></li></ul><ul id="2342881f-86f1-80f9-95e6-e617860a8f71" class="bulleted-list"><li style="list-style-type:circle"><strong>HEX Preview:</strong> <code>#A8D4FF</code> at 70% opacity</li></ul></li></ul><ul id="2342881f-86f1-802b-8404-fde495c96e27" class="bulleted-list"><li style="list-style-type:disc"><strong>Glow (Box-Shadow):</strong><ul id="2342881f-86f1-8006-bc9d-efe80747b8ef" class="bulleted-list"><li style="list-style-type:circle"><code>box-shadow: 0 0 16px 2px rgba(168, 212, 255, 0.18)</code></li></ul><ul id="2342881f-86f1-806a-b715-edabc6f9eadc" class="bulleted-list"><li style="list-style-type:circle"><strong>HEX Preview:</strong> <code>#A8D4FF</code> at 18% opacity</li></ul></li></ul><hr id="2342881f-86f1-8068-b088-e05454891c37"/><h3 id="2342881f-86f1-802d-9aa0-d920b806cd71" class=""><strong>Resize Handles</strong></h3><ul id="2342881f-86f1-8034-97ac-df51b45733e6" class="bulleted-list"><li style="list-style-type:disc"><strong>Normal:</strong><ul id="2342881f-86f1-80a3-8c69-de46c814e849" class="bulleted-list"><li style="list-style-type:circle"><code>background: rgba(168, 212, 255, 0.70)</code></li></ul><ul id="2342881f-86f1-801d-ae3b-e233a6978c39" class="bulleted-list"><li style="list-style-type:circle"><strong>HEX:</strong> <code>#A8D4FF</code> at 70%</li></ul></li></ul><ul id="2342881f-86f1-8088-b8c4-eb0922862dc5" class="bulleted-list"><li style="list-style-type:disc"><strong>On Hover:</strong><ul id="2342881f-86f1-8056-be86-d1ca2aee5dfa" class="bulleted-list"><li style="list-style-type:circle"><code>background: rgba(127, 211, 255, 0.85)</code></li></ul><ul id="2342881f-86f1-80e2-bba0-c3943032d6b1" class="bulleted-list"><li style="list-style-type:circle"><strong>HEX:</strong> <code>#7FD3FF</code> at 85%</li></ul></li></ul><hr id="2342881f-86f1-803f-8b4b-c96151cb1007"/><h3 id="2342881f-86f1-8008-93f2-f1f3c8022854" class=""><strong>Quick Action Buttons (Add, Remove)</strong></h3><ul id="2342881f-86f1-8095-8c26-db7641179b65" class="bulleted-list"><li style="list-style-type:disc"><strong>Default:</strong><ul id="2342881f-86f1-803a-8141-d7207522115f" class="bulleted-list"><li style="list-style-type:circle"><code>background: rgba(247, 250, 255, 0.80)</code></li></ul><ul id="2342881f-86f1-8047-82e6-f6904e2e5c27" class="bulleted-list"><li style="list-style-type:circle"><code>color: #0A2540</code> <em>(Dark blue for icon/text)</em></li></ul></li></ul><ul id="2342881f-86f1-8059-bae7-e1cc73827fd0" class="bulleted-list"><li style="list-style-type:disc"><strong>Hover/Active:</strong><ul id="2342881f-86f1-804a-8d6d-ea83cfd0aa09" class="bulleted-list"><li style="list-style-type:circle"><code>background: rgba(127, 211, 255, 0.32)</code></li></ul><ul id="2342881f-86f1-804d-bfb1-c1e1b188429d" class="bulleted-list"><li style="list-style-type:circle"><strong>Glow:</strong> <code>box-shadow: 0 0 6px 2px #7FD3FF55</code></li></ul></li></ul><hr id="2342881f-86f1-808a-a6a3-f4b6819a5ab5"/><h3 id="2342881f-86f1-8070-9b57-d8af017ff9cf" class=""><strong>Editable Fields (Liquid Glass Input)</strong></h3><ul id="2342881f-86f1-8082-9967-d6ea61ac579c" class="bulleted-list"><li style="list-style-type:disc"><strong>Field Background:</strong><ul id="2342881f-86f1-80ad-852d-cb43f25bd89b" class="bulleted-list"><li style="list-style-type:circle"><code>background: rgba(247, 250, 255, 0.72)</code></li></ul><ul id="2342881f-86f1-8095-bc44-e28816e69045" class="bulleted-list"><li style="list-style-type:circle"><strong>HEX:</strong> <code>#F7FAFF</code> at 72%</li></ul></li></ul><ul id="2342881f-86f1-80da-b8b9-ca3459d5d726" class="bulleted-list"><li style="list-style-type:disc"><strong>Focus State:</strong><ul id="2342881f-86f1-80e2-ba02-df1c925e37a3" class="bulleted-list"><li style="list-style-type:circle"><code>border: 2px solid #A8D4FF</code></li></ul><ul id="2342881f-86f1-80fd-8a6f-feb4f0866776" class="bulleted-list"><li style="list-style-type:circle"><code>box-shadow: 0 0 6px 2px #7FD3FF88</code></li></ul></li></ul><ul id="2342881f-86f1-8001-8734-f0039467d6fc" class="bulleted-list"><li style="list-style-type:disc"><strong>Label/Text:</strong><ul id="2342881f-86f1-8083-b963-c5df9a5051f8" class="bulleted-list"><li style="list-style-type:circle"><code>color: #1C2E3B</code> <em>(Charcoal blue for clarity)</em></li></ul></li></ul><ul id="2342881f-86f1-809f-86b3-c8b4bda7a983" class="bulleted-list"><li style="list-style-type:disc"><strong>Unit Hint:</strong><ul id="2342881f-86f1-8062-acf3-dadb12b4875b" class="bulleted-list"><li style="list-style-type:circle"><code>color: #6A89A6</code> <em>(Soft slate blue)</em></li></ul></li></ul><hr id="2342881f-86f1-8052-91cc-c44ad8d1ce60"/><h3 id="2342881f-86f1-8011-9f47-ee5e64c08366" class=""><strong>Status Badges</strong></h3><ul id="2342881f-86f1-8060-86d4-cfee7a8bfe7e" class="bulleted-list"><li style="list-style-type:disc"><strong>Warning (Critical):</strong><ul id="2342881f-86f1-807d-b0b0-ffb1c670c21b" class="bulleted-list"><li style="list-style-type:circle"><code>background: #F54C4C</code> <em>(red)</em></li></ul><ul id="2342881f-86f1-80aa-a81e-e3dc9ede7c65" class="bulleted-list"><li style="list-style-type:circle"><code>color: #FFF</code></li></ul></li></ul><ul id="2342881f-86f1-8062-8a65-e4943a49ea47" class="bulleted-list"><li style="list-style-type:disc"><strong>Warning (Caution):</strong><ul id="2342881f-86f1-803a-bb44-d213c79ecec6" class="bulleted-list"><li style="list-style-type:circle"><code>background: #F8B037</code> <em>(yellow/orange)</em></li></ul><ul id="2342881f-86f1-80fe-86bd-f7d76da70cbf" class="bulleted-list"><li style="list-style-type:circle"><code>color: #191919</code></li></ul></li></ul><ul id="2342881f-86f1-8094-8ca6-e1bb71526439" class="bulleted-list"><li style="list-style-type:disc"><strong>Warning (Info):</strong><ul id="2342881f-86f1-8002-acc3-cc4292df7230" class="bulleted-list"><li style="list-style-type:circle"><code>background: #46C6EF</code> <em>(blue)</em></li></ul><ul id="2342881f-86f1-8079-807c-fc5fef8d2dcd" class="bulleted-list"><li style="list-style-type:circle"><code>color: #fff</code></li></ul></li></ul><ul id="2342881f-86f1-804e-bfc3-f1572968476c" class="bulleted-list"><li style="list-style-type:disc"><strong>Lock/Read-Only:</strong><ul id="2342881f-86f1-8025-a816-ffbb8cfc7a19" class="bulleted-list"><li style="list-style-type:circle"><code>background: #E6EAF1</code></li></ul><ul id="2342881f-86f1-800b-8a0b-c4d0fa76de16" class="bulleted-list"><li style="list-style-type:circle"><code>color: #8395B0</code></li></ul></li></ul><ul id="2342881f-86f1-8051-9c10-fc36163e2496" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip/Info:</strong><ul id="2342881f-86f1-8036-bd3d-e848e6d72b0b" class="bulleted-list"><li style="list-style-type:circle"><code>background: #1E365B</code></li></ul><ul id="2342881f-86f1-80ba-a0b8-c21d167e113d" class="bulleted-list"><li style="list-style-type:circle"><code>color: #FFFFFF</code></li></ul><ul id="2342881f-86f1-801a-81ab-e6f1c8b3b12b" class="bulleted-list"><li style="list-style-type:circle"><em>Shadow:</em> <code>box-shadow: 0 2px 8px 0 #1E365B33</code></li></ul></li></ul><hr id="2342881f-86f1-8020-8b24-ee69e26e8967"/><h3 id="2342881f-86f1-809b-8d0c-ee066e84cdbb" class=""><strong>Panel Shadow/Outer Glow</strong></h3><ul id="2342881f-86f1-80bc-ad90-fc4e977eefe1" class="bulleted-list"><li style="list-style-type:disc"><strong>General:</strong><ul id="2342881f-86f1-80fc-b6dd-fe7737c0fb62" class="bulleted-list"><li style="list-style-type:circle"><code>box-shadow: 0 4px 40px 0 rgba(127, 211, 255, 0.14)</code></li></ul></li></ul><hr id="2342881f-86f1-80b4-a278-d9f5a573bae1"/><h2 id="2342881f-86f1-8033-aea1-eebd2118f451" class=""><strong>Quick Visual Reference Table</strong></h2><table id="2342881f-86f1-804d-ac8d-c0dff1bf4189" class="simple-table"><thead class="simple-table-header"><tr id="2342881f-86f1-8058-8c9b-ed5cdaefdada"><th id="REnT" class="simple-table-header-color simple-table-header">Element</th><th id="C{IO" class="simple-table-header-color simple-table-header">HEX/Alpha Example</th><th id="CYNv" class="simple-table-header-color simple-table-header">Usage Example</th></tr></thead><tbody><tr id="2342881f-86f1-80a8-b4fa-c04443da619f"><td id="REnT" class="">Frosted Glass</td><td id="C{IO" class="">#F7FAFF / 55–72%</td><td id="CYNv" class="">Panel, Fields</td></tr><tr id="2342881f-86f1-8050-a070-d283e6d3fd51"><td id="REnT" class="">Liquid Glass Border</td><td id="C{IO" class="">#A8D4FF / 70%</td><td id="CYNv" class="">Panel Border, Handles</td></tr><tr id="2342881f-86f1-8096-b43e-dc04d555b894"><td id="REnT" class="">Resize Handle Hover</td><td id="C{IO" class="">#7FD3FF / 85%</td><td id="CYNv" class="">Handle on Hover</td></tr><tr id="2342881f-86f1-803f-aee8-c19783b3e18b"><td id="REnT" class="">Quick Action Button</td><td id="C{IO" class="">#F7FAFF / 80%</td><td id="CYNv" class="">Add, Remove, etc.</td></tr><tr id="2342881f-86f1-8037-ae8d-f02f19d71272"><td id="REnT" class="">Quick Action Hover</td><td id="C{IO" class="">#7FD3FF / 32%</td><td id="CYNv" class="">Button on Hover</td></tr><tr id="2342881f-86f1-80b0-addc-d868de6c295b"><td id="REnT" class="">Field Label/Text</td><td id="C{IO" class="">#1C2E3B</td><td id="CYNv" class="">Labels, Values</td></tr><tr id="2342881f-86f1-80aa-9cb6-c3ed6a70eeed"><td id="REnT" class="">Field Unit Hint</td><td id="C{IO" class="">#6A89A6</td><td id="CYNv" class="">CFM, FPM, etc.</td></tr><tr id="2342881f-86f1-802a-a98a-f3ac059c60b5"><td id="REnT" class="">Warning Critical</td><td id="C{IO" class="">#F54C4C</td><td id="CYNv" class="">Velocity exceed</td></tr><tr id="2342881f-86f1-802c-962f-c402cbee78de"><td id="REnT" class="">Warning Caution</td><td id="C{IO" class="">#F8B037</td><td id="CYNv" class="">Near-limit alert</td></tr><tr id="2342881f-86f1-8069-85d0-c93651c7675a"><td id="REnT" class="">Warning Info</td><td id="C{IO" class="">#46C6EF</td><td id="CYNv" class="">Info badge</td></tr><tr id="2342881f-86f1-800a-9c8e-f15320024df0"><td id="REnT" class="">Lock Indicator</td><td id="C{IO" class="">#E6EAF1 / #8395B0</td><td id="CYNv" class="">Read-only field badge</td></tr><tr id="2342881f-86f1-808c-a262-f725fdff1b3a"><td id="REnT" class="">Info Tooltip</td><td id="C{IO" class="">#1E365B / #FFFFFF</td><td id="CYNv" class="">Standard ref/info</td></tr><tr id="2342881f-86f1-804c-854e-e86acd058c0e"><td id="REnT" class="">Panel Shadow</td><td id="C{IO" class="">#7FD3FF / 14%</td><td id="CYNv" class="">Subtle glow/outer shadow</td></tr></tbody></table></details></li></ul></details></li></ul><hr id="2332881f-86f1-80fd-be64-e7c0968b24c1"/><ul id="2332881f-86f1-809d-8cd7-ce6374b8779b" class="toggle"><li><details open=""><summary><strong>6. Model Summary Panel (Result &amp; Warnings)</strong></summary><hr id="2342881f-86f1-80c7-84d3-f0cd37941066"/><p id="2342881f-86f1-8002-a6b3-ef97d99cd6e2" class=""><strong>1. Naming</strong></p><ul id="2342881f-86f1-80d2-b937-d1191a836e69" class="bulleted-list"><li style="list-style-type:disc"><strong>Element Name:</strong> Model Summary Panel<p id="2342881f-86f1-805f-9a52-c6d745b02d18" class=""><em>(Use “panel” for clarity, as it’s a movable, dockable UI surface rather than a bar or pop-up.)</em></p></li></ul><ul id="2342881f-86f1-80ba-9f5d-e7c87fd35f7a" class="bulleted-list"><li style="list-style-type:disc"><strong>Trigger/Button Name (in Status Bar):</strong> Summary</li></ul><hr id="2342881f-86f1-800c-b525-f8efaf9d75b3"/><p id="2342881f-86f1-8016-8a9f-c8f106f4170a" class=""><strong>2. Core Purpose</strong></p><blockquote id="2342881f-86f1-80e0-a015-f2b44a0dc80f" class="">Primary function:<p id="2342881f-86f1-8043-b919-eeb49d0bb068" class="">Provide a unified, system-by-system summary of computational results, fan sizing and CFM requirements (per SMACNA/ASHRAE/UL/local code), and a real-time warning log, with report export/copy and quick navigation to errors.</p></blockquote><hr id="2342881f-86f1-8060-9524-cae1b71770cf"/><p id="2342881f-86f1-804c-93e6-ef6970e0cfe5" class=""><strong>3. Behavior &amp; Interaction</strong></p><h3 id="2342881f-86f1-805d-9cd6-c621960767d7" class=""><strong>A. Launch/Visibility</strong></h3><ul id="2342881f-86f1-806b-9bc6-d270a8cfb46c" class="bulleted-list"><li style="list-style-type:disc"><strong>Appears when user clicks the “Summary” button</strong> in the status bar.</li></ul><ul id="2342881f-86f1-8008-88d8-f880f07f68e0" class="bulleted-list"><li style="list-style-type:disc"><strong>Dock effect:</strong> Slides in (docks) at the bottom of the viewport above the status bar, or floats as a movable, resizable panel if undocked.</li></ul><ul id="2342881f-86f1-807e-97b4-f042992f9dbb" class="bulleted-list"><li style="list-style-type:disc"><strong>Panel is always-on-top</strong>, but does <strong>not overlap</strong> the drawn model or any core workspace controls.</li></ul><ul id="2342881f-86f1-80e8-a977-cfe0bdde08e1" class="bulleted-list"><li style="list-style-type:disc"><strong>Pin option:</strong> User may pin the panel in place; if pinned, panel avoids overlapping important on-screen content (auto reposition if needed).</li></ul><h3 id="2342881f-86f1-803a-9529-e26045fb9ca0" class=""><strong>B. Dismissal</strong></h3><ul id="2342881f-86f1-808c-8aa7-c95d13e47626" class="bulleted-list"><li style="list-style-type:disc">Close via “X” button, <code>Esc</code> key, or clicking outside (if floating).</li></ul><ul id="2342881f-86f1-809d-a9a6-c08f69c9ddc6" class="bulleted-list"><li style="list-style-type:disc"><strong>Docked state:</strong> Panel collapses down/out of viewport with smooth animation.</li></ul><hr id="2342881f-86f1-806d-b42d-c4074ea27228"/><h3 id="2342881f-86f1-8070-8728-fa82e9abb618" class=""><strong>4. Layout &amp; Content</strong></h3><h3 id="2342881f-86f1-8043-9c9a-e122cd87bf64" class=""><strong>A. System Selector</strong></h3><ul id="2342881f-86f1-803b-b905-f2bcfb1888d8" class="bulleted-list"><li style="list-style-type:disc"><strong>Dropdown or segmented control</strong> at panel top.</li></ul><ul id="2342881f-86f1-80eb-8ed1-d5856f16a936" class="bulleted-list"><li style="list-style-type:disc">User can switch between results for:<ul id="2342881f-86f1-80aa-a8e2-ee01af201d06" class="bulleted-list"><li style="list-style-type:circle">Each unique duct “system” (branch, rooftop, single main run, etc.)</li></ul><ul id="2342881f-86f1-807f-9a23-c6170ebf4d59" class="bulleted-list"><li style="list-style-type:circle">All systems combined (aggregate)</li></ul></li></ul><ul id="2342881f-86f1-80de-b7db-de73585429ea" class="bulleted-list"><li style="list-style-type:disc"><strong>Selection state</strong> is visually prominent and sticky until changed.</li></ul><h3 id="2342881f-86f1-8058-80b0-dd7f13ed4915" class=""><strong>B. Live Results Section</strong></h3><ul id="2342881f-86f1-8026-b690-c64892e060d1" class="bulleted-list"><li style="list-style-type:disc"><strong>Fan Requirement</strong> (computed by system):<ul id="2342881f-86f1-8058-8edc-c14074cd1d85" class="bulleted-list"><li style="list-style-type:circle">Fan static pressure (in. W.C. / Pa)</li></ul><ul id="2342881f-86f1-80ff-bb50-ff7cf1c6ec2a" class="bulleted-list"><li style="list-style-type:circle">Fan airflow (CFM / L/s)</li></ul><ul id="2342881f-86f1-80a2-9ee2-eabb1fba033f" class="bulleted-list"><li style="list-style-type:circle">Recommended fan model (if implemented, else leave out)</li></ul></li></ul><ul id="2342881f-86f1-8083-b9b6-ff266a115ea5" class="bulleted-list"><li style="list-style-type:disc"><strong>CFM Requirement</strong> per room (for all rooms in system)</li></ul><ul id="2342881f-86f1-8000-bc14-f1c09a7fe2d5" class="bulleted-list"><li style="list-style-type:disc"><strong>Total Duct Pressure Loss</strong> (for current system)</li></ul><ul id="2342881f-86f1-80fe-94ca-e30d7954d1cb" class="bulleted-list"><li style="list-style-type:disc"><strong>Length and Size Summary</strong> (total length, average/main size)</li></ul><ul id="2342881f-86f1-80de-8cda-f303fd828beb" class="bulleted-list"><li style="list-style-type:disc"><strong>Copy Button:</strong> Copies the visible results as a computation report (plain text, rich text, or CSV).</li></ul><h3 id="2342881f-86f1-8037-9a07-dc0d04bbf57c" class=""><strong>C. Warnings Section</strong></h3><ul id="2342881f-86f1-805e-9efc-e59a62f6bd2d" class="bulleted-list"><li style="list-style-type:disc"><strong>Warnings Badge/Icon:</strong><ul id="2342881f-86f1-8026-8c83-e2fc9f355e49" class="bulleted-list"><li style="list-style-type:circle">Shows total count (live), color-coded:<ul id="2342881f-86f1-8046-a434-ee590d53040a" class="bulleted-list"><li style="list-style-type:square"><strong>Critical:</strong> Red/white</li></ul><ul id="2342881f-86f1-807e-8224-d732ac8bdd8b" class="bulleted-list"><li style="list-style-type:square"><strong>Caution:</strong> Yellow/white</li></ul><ul id="2342881f-86f1-80e0-820b-c5d59739790d" class="bulleted-list"><li style="list-style-type:square"><strong>Info:</strong> Neutral/grey/white</li></ul></li></ul></li></ul><ul id="2342881f-86f1-8002-ab74-feb58fd12af8" class="bulleted-list"><li style="list-style-type:disc"><strong>Codes Referenced:</strong><ul id="2342881f-86f1-807a-a59c-ef988db6e44f" class="bulleted-list"><li style="list-style-type:circle">Shows all standards in use (SMACNA, ASHRAE, UL, Local).</li></ul><ul id="2342881f-86f1-8003-87d8-ca3b32e88030" class="bulleted-list"><li style="list-style-type:circle">Each warning references the specific code/rule/section, and what triggered it.</li></ul><ul id="2342881f-86f1-80e7-840d-db3bc5d8f83e" class="bulleted-list"><li style="list-style-type:circle">Local codes: Displayed if user inputted; otherwise, omitted.</li></ul></li></ul><ul id="2342881f-86f1-8019-9619-d8d98305a05b" class="bulleted-list"><li style="list-style-type:disc"><strong>Warning List:</strong><ul id="2342881f-86f1-802e-9a37-c32eeb5621d8" class="bulleted-list"><li style="list-style-type:circle"><strong>Critical</strong> first, then caution, then info.</li></ul><ul id="2342881f-86f1-8094-965d-f4199d223544" class="bulleted-list"><li style="list-style-type:circle">Each warning is actionable: <strong>Clicking a warning zooms to/highlights</strong> affected duct in the drawing (jump-to-error).</li></ul><ul id="2342881f-86f1-80f6-b85c-ce0e800f9c9d" class="bulleted-list"><li style="list-style-type:circle">Affected portions of the ductwork in the canvas <strong>glow</strong> (yellow for caution, red for critical) as long as warnings persist.</li></ul><ul id="2342881f-86f1-8074-8097-c23567b6f750" class="bulleted-list"><li style="list-style-type:circle">If all OK, shows a “Compliant” badge with code references.</li></ul></li></ul><h3 id="2342881f-86f1-802d-bc3f-db2529a3ac97" class=""><strong>D. Tips Icon</strong></h3><ul id="2342881f-86f1-8075-8b7b-e90b027eefe8" class="bulleted-list"><li style="list-style-type:disc"><strong>Small “Tips” icon</strong> (lightbulb or “?”) at upper-right corner of panel.<ul id="2342881f-86f1-801b-adb5-d43c217bc148" class="bulleted-list"><li style="list-style-type:circle">Click opens a context-aware mini-window with usage tips or best practices (placement: floating above/beside the icon, not overlapping key results).</li></ul></li></ul><hr id="2342881f-86f1-806a-b83d-e3ce7b16e64b"/><h3 id="2342881f-86f1-80d8-9331-e48576731ff7" class=""><strong>5. Panel Style &amp; Accessibility</strong></h3><h3 id="2342881f-86f1-80ff-8728-c04a8a0b07a8" class=""><strong>A. Visual Design</strong></h3><ul id="2342881f-86f1-80eb-ad4a-f67c350e984b" class="bulleted-list"><li style="list-style-type:disc"><strong>All-white/neutral palette</strong>:<ul id="2342881f-86f1-8077-89b2-e5b3239e2675" class="bulleted-list"><li style="list-style-type:circle">Panel BG: <code>rgba(255,255,255,0.60)</code> (Frosted glass)</li></ul><ul id="2342881f-86f1-80d8-9f89-e492ef8c7c93" class="bulleted-list"><li style="list-style-type:circle">Borders/shadow: <code>rgba(255,255,255,0.80)</code></li></ul><ul id="2342881f-86f1-80a0-902a-cbb4dbaddd9b" class="bulleted-list"><li style="list-style-type:circle">Section headers: #2D2D2D or #222428</li></ul><ul id="2342881f-86f1-8016-98a9-f0a7918005aa" class="bulleted-list"><li style="list-style-type:circle">Warnings: Use white badge w/ colored text &amp; subtle colored glow only for contrast</li></ul><ul id="2342881f-86f1-80c5-b3a4-d1a141dd20e9" class="bulleted-list"><li style="list-style-type:circle">Copy Button: Subtle outline, hover raises with 6% more opacity</li></ul><ul id="2342881f-86f1-800a-ae2b-d74c84025a93" class="bulleted-list"><li style="list-style-type:circle">Tips Icon: Light outline, hover effect</li></ul></li></ul><ul id="2342881f-86f1-801e-9860-cf4f5a5dbcf4" class="bulleted-list"><li style="list-style-type:disc"><strong>Panel shadow</strong>: Soft white, never “pop” blue or orange.</li></ul><h3 id="2342881f-86f1-80cb-ac5e-ce36b0290c7d" class=""><strong>B. Accessibility</strong></h3><ul id="2342881f-86f1-80c4-a953-c1fdaa3f6952" class="bulleted-list"><li style="list-style-type:disc"><strong>Keyboard navigation:</strong><ul id="2342881f-86f1-80d3-ba1b-dcf44598d9b7" class="bulleted-list"><li style="list-style-type:circle">Tab between system selector, live results, copy button, warning list, tips icon.</li></ul><ul id="2342881f-86f1-80f8-840f-e38f1d0a5c8f" class="bulleted-list"><li style="list-style-type:circle">All warnings/fields accessible by screen reader, with ARIA labels referencing codes.</li></ul><ul id="2342881f-86f1-8007-aa8a-dade6365ab1a" class="bulleted-list"><li style="list-style-type:circle">Focus outline is visible but subtle (e.g., 2px #E5E5E5)</li></ul></li></ul><ul id="2342881f-86f1-80c7-b7cc-c06b22e7d3e1" class="bulleted-list"><li style="list-style-type:disc"><strong>Responsive/dockable:</strong><ul id="2342881f-86f1-8047-8bdc-fdcd2edb1c7e" class="bulleted-list"><li style="list-style-type:circle">Panel shrinks on small screens, scrolls if needed.</li></ul></li></ul><hr id="2342881f-86f1-80e6-a383-c6dc4fa7af31"/><h3 id="2342881f-86f1-8028-a72c-e64ef98acb61" class=""><strong>6. Example Layout (Wireframe Block)</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2342881f-86f1-803c-a6de-c4712f201098" class="code"><code class="language-Plain Text">╭───────────────────────────── Model Summary Panel ─────────────────────────────╮
│ System: [ Rooftop 1 ▼ ]         [ Copy Report ]               [ Tips (i) ]   │
│                                                                             │
│  Fan Requirement:           3200 CFM   |   2.1 in. W.C.                     │
│  Pressure Loss:             1.7 in. W.C.   |   Total Length: 128 ft         │
│  Room CFM:                  Room 101 – 300, Room 102 – 450, ...             │
│                                                                             │
│  Warnings  [ 2 Critical | 1 Caution ]  [Show Codes: SMACNA 2021 §5-1, ... ] │
│  ──────────────────────────────────────────────────────────────────────────  │
│   [!] Velocity exceeds SMACNA Table 4-1 in branch (Duct B), jump-to-error    │
│   [!] Pressure loss over 2 in. W.C. in Main Run, jump-to-error               │
│   [⚠] CFM below room spec in Room 103, jump-to-error                         │
│   [✓] Compliant with all referenced codes (if no warnings)                   │
╰──────────────────────────────────────────────────────────────────────────────╯
</code></pre><hr id="2342881f-86f1-80a4-957b-d03787d98cca"/><h3 id="2342881f-86f1-80b1-a150-d6e70ddcf679" class=""><strong>7. Rationale</strong></h3><ul id="2342881f-86f1-8022-a015-cad21e5db979" class="bulleted-list"><li style="list-style-type:disc"><strong>Why this approach:</strong><ul id="2342881f-86f1-8004-8c12-cafcfb4a5e87" class="bulleted-list"><li style="list-style-type:circle">Keeps all essential <em>model-level</em> results and compliance in one persistent place, without clutter or redundancy.</li></ul><ul id="2342881f-86f1-8090-8d84-fd5ee454a656" class="bulleted-list"><li style="list-style-type:circle"><em>System selector</em> supports real HVAC design workflow (multiple systems per project).</li></ul><ul id="2342881f-86f1-8060-8ce2-dbdb2a398d88" class="bulleted-list"><li style="list-style-type:circle"><em>Copy button</em> streamlines reporting/export.</li></ul><ul id="2342881f-86f1-8083-8c8d-f6d98859667d" class="bulleted-list"><li style="list-style-type:circle"><em>Warnings</em> map clearly to drawn elements, using real codes—improves traceability and compliance.</li></ul><ul id="2342881f-86f1-80b5-a42f-e5bd9b17c7b5" class="bulleted-list"><li style="list-style-type:circle"><em>Tips</em> icon provides just-in-time guidance, not cluttering the main results.</li></ul></li></ul><hr id="2342881f-86f1-80f0-b837-c88a96fa3c60"/><h3 id="2342881f-86f1-80b1-8c9e-f46bbfc25e72" class=""><strong>8. Interactivity Recap</strong></h3><ul id="2342881f-86f1-805f-8e3e-cc8a493291c8" class="bulleted-list"><li style="list-style-type:disc"><strong>Model Summary Panel</strong> appears docked or floating on click, always above Status Bar.</li></ul><ul id="2342881f-86f1-80e2-9840-c79a4617ce0e" class="bulleted-list"><li style="list-style-type:disc"><strong>System selector</strong> toggles between system results.</li></ul><ul id="2342881f-86f1-8073-ae75-e878bcf13482" class="bulleted-list"><li style="list-style-type:disc"><strong>Copy</strong>: Instantly copies formatted computation results.</li></ul><ul id="2342881f-86f1-8008-b62e-fe58b2d8407d" class="bulleted-list"><li style="list-style-type:disc"><strong>Warnings</strong>: Live, color-coded, jump-to-error in canvas, fully actionable.</li></ul><ul id="2342881f-86f1-800c-abce-cef1acbe9f06" class="bulleted-list"><li style="list-style-type:disc"><strong>Tips</strong>: Always available, never intrusive.</li></ul><ul id="2342881f-86f1-80e6-a024-cecf46710287" class="bulleted-list"><li style="list-style-type:disc"><strong>Pin:</strong> Keeps panel persistent if desired, but intelligently avoids overlap.</li></ul><ul id="2342881f-86f1-80f3-9d6f-d868439e0527" class="bulleted-list"><li style="list-style-type:disc"><strong>Closes</strong> with X, Esc, or click-away.</li></ul></details></li></ul><hr id="2332881f-86f1-8016-8b5d-d63a3de7ed21"/><ul id="2332881f-86f1-806c-8d8a-d9952c5951d2" class="toggle"><li><details open=""><summary><strong>7. Status Bar</strong></summary><h1 id="2352881f-86f1-80d4-bfbf-c5965b1cbe25" class=""><strong>Status Bar </strong></h1><hr id="2352881f-86f1-8077-b19e-c5662531c58a"/><h2 id="2352881f-86f1-8050-9f7d-c83ad1f93422" class=""><strong>Overview</strong></h2><p id="2352881f-86f1-80b6-ab54-f57c76c3eef2" class="">The <strong>Status Bar</strong> is a single-line, always-visible UI strip, docked at the absolute bottom of the Air Duct Sizer 3D Tool workspace.</p><p id="2352881f-86f1-80ec-a01f-cbc81a92f90c" class="">It serves as the command center for global actions, state, and quick reference—<strong>never for navigation or tool switching</strong>.</p><p id="2352881f-86f1-8053-9eaf-ef770ce9fccc" class=""><strong>Key features:</strong></p><ul id="2352881f-86f1-803c-94ef-e8de352bdc4d" class="bulleted-list"><li style="list-style-type:disc">Minimal vertical height (≤40 px), never wraps or grows vertically</li></ul><ul id="2352881f-86f1-8010-b5d3-f8db98afd716" class="bulleted-list"><li style="list-style-type:disc">All elements strictly horizontally aligned; adjusts dynamically as elements expand/collapse</li></ul><ul id="2352881f-86f1-80f3-9e43-f84e3e4e8a77" class="bulleted-list"><li style="list-style-type:disc">Maximum accessibility and professional clarity</li></ul><ul id="2352881f-86f1-806f-8353-fe27fd376fc2" class="bulleted-list"><li style="list-style-type:disc">All icons, controls, and states have descriptive ARIA labels and tooltips</li></ul><hr id="2352881f-86f1-801b-9b04-fbd82791e3b5"/><h2 id="2352881f-86f1-80bf-97af-c34b1222d8a6" class=""><strong>Layout: Left to Right Order (Final)</strong></h2><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2352881f-86f1-8089-996f-f052b2968763" class="code"><code class="language-Plain Text">╭───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮
│ [⏳▾ Version Control] [● Save Status] [● Connection] [◩ Grid Toggle] [⚠️ Warning Glow Toggle] [▤ Model Summary] [🔍 Search…] [Units ▼] │
╰───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯
</code></pre><ul id="2352881f-86f1-809d-bf30-e70cb0ff8e3b" class="bulleted-list"><li style="list-style-type:disc"><strong>Single line</strong></li></ul><ul id="2352881f-86f1-80b4-857e-e7a1ebd4eb3f" class="bulleted-list"><li style="list-style-type:disc"><strong>No stacking or wrapping, ever</strong></li></ul><ul id="2352881f-86f1-80f9-b965-e991c433f2e7" class="bulleted-list"><li style="list-style-type:disc"><strong>Expanding elements shift siblings smoothly to avoid overlap</strong></li></ul><ul id="2352881f-86f1-80f2-850f-cf195024bcb0" class="bulleted-list"><li style="list-style-type:disc"><strong>All controls ARIA-labeled, tooltipped, and tab-order consistent</strong></li></ul><ul id="2352881f-86f1-805a-baa9-f98d7abbc696" class="bulleted-list"><li style="list-style-type:disc"><strong>Docked to the absolute bottom of the viewport</strong></li></ul><hr id="2352881f-86f1-80d6-b4f1-df10cfcf5ff4"/><h2 id="2352881f-86f1-806f-853b-e297ea1c618a" class=""><strong>Element-by-Element Specification</strong></h2><hr id="2352881f-86f1-80aa-b445-c00613f4b5a9"/><h3 id="2352881f-86f1-80f1-a4ee-c0a3bfbdf310" class=""><strong>1. Version Control</strong></h3><ul id="2352881f-86f1-800d-9bc2-e659063f84fe" class="bulleted-list"><li style="list-style-type:disc"><strong>Expert Name:</strong> Version Control</li></ul><ul id="2352881f-86f1-8071-8e64-f3726e7e9e6a" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong> ⏳ with caret/flyout indicator (▾)</li></ul><ul id="2352881f-86f1-805b-8abb-f69db7501053" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Flyout/Popover panel (expands up or overlays upward above bar)</li></ul><ul id="2352881f-86f1-800c-ac17-e9d55d066f50" class="bulleted-list"><li style="list-style-type:disc"><strong>Function:</strong><ul id="2352881f-86f1-80d8-a2b3-dd7dad91170a" class="bulleted-list"><li style="list-style-type:circle">View version history (auto-save, manual saves, restore points)</li></ul><ul id="2352881f-86f1-80c8-be70-f0f1d2554f42" class="bulleted-list"><li style="list-style-type:circle">Preview and restore previous states</li></ul></li></ul><ul id="2352881f-86f1-80be-bc76-d1cb4f715c13" class="bulleted-list"><li style="list-style-type:disc"><strong>Behavior:</strong><ul id="2352881f-86f1-806d-bf9c-d38788115485" class="bulleted-list"><li style="list-style-type:circle">Click opens version list (timestamps, labels, actions)</li></ul><ul id="2352881f-86f1-808e-be0c-c16fef3345f1" class="bulleted-list"><li style="list-style-type:circle">Only one flyout open at a time; keyboard navigable</li></ul></li></ul><ul id="2352881f-86f1-80b5-9b8c-ef53eac3ebc8" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip:</strong> “Version history: view and restore previous saves”</li></ul><ul id="2352881f-86f1-80e2-a21e-dcdd935b771b" class="bulleted-list"><li style="list-style-type:disc"><strong>Accessibility:</strong> ARIA role=“menu/button”; labeled by icon and tooltip; full keyboard support</li></ul><ul id="2352881f-86f1-8020-96e5-fd18713a42d1" class="bulleted-list"><li style="list-style-type:disc"><strong>Responsive Behavior:</strong> Collapses to icon-only on narrow widths; always accessible by tabbing</li></ul><hr id="2352881f-86f1-80dd-b1a9-efa2c4c85235"/><h3 id="2352881f-86f1-803a-9cac-d83e38b09516" class=""><strong>2. Save Status</strong></h3><ul id="2352881f-86f1-8043-9b0c-e0d554338369" class="bulleted-list"><li style="list-style-type:disc"><strong>Expert Name:</strong> Save Status</li></ul><ul id="2352881f-86f1-80f1-8fdc-c91e3b21a6c9" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong> Animated dot ● (plus text, e.g. “All changes saved”)</li></ul><ul id="2352881f-86f1-8019-afe9-c7b6c0127241" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Status chip (passive for auto-save; active button if manual save)</li></ul><ul id="2352881f-86f1-8094-a256-f0ae9cef5ce5" class="bulleted-list"><li style="list-style-type:disc"><strong>States:</strong><ul id="2352881f-86f1-80c5-ad2f-c835cd0737da" class="bulleted-list"><li style="list-style-type:circle"><strong>Auto-Save:</strong> “All changes saved ●” (green dot, pulsing if saving in progress)</li></ul><ul id="2352881f-86f1-80dc-a1df-d3658c691edd" class="bulleted-list"><li style="list-style-type:circle"><strong>Manual Save:</strong> “Save Now” (active, highlighted when unsaved changes)</li></ul></li></ul><ul id="2352881f-86f1-80bc-a185-e82279d54063" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip:</strong> “Auto-save enabled” or “Manual save—click to save now”</li></ul><ul id="2352881f-86f1-809a-b64f-cff05427aceb" class="bulleted-list"><li style="list-style-type:disc"><strong>Accessibility:</strong> ARIA live region; full keyboard/reader support</li></ul><ul id="2352881f-86f1-8068-b2e5-e639b323ac5d" class="bulleted-list"><li style="list-style-type:disc"><strong>Responsive Behavior:</strong> Text may collapse to just icon if bar contracts</li></ul><hr id="2352881f-86f1-80a6-80d1-ca407fc49398"/><h3 id="2352881f-86f1-800a-b0d7-d669c6b2b117" class=""><strong>3. Connection Status</strong></h3><ul id="2352881f-86f1-809e-9006-db37bfd7504d" class="bulleted-list"><li style="list-style-type:disc"><strong>Expert Name:</strong> Connection Status</li></ul><ul id="2352881f-86f1-80d0-83f7-e6f07ac6b753" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong> Single dot ●</li></ul><ul id="2352881f-86f1-8064-84e5-cba4fa4e1ca3" class="bulleted-list"><li style="list-style-type:disc"><strong>Colors:</strong><ul id="2352881f-86f1-8073-ad11-c6b6a57ca820" class="bulleted-list"><li style="list-style-type:circle">Online: #00C853 (Green)</li></ul><ul id="2352881f-86f1-80d9-ac09-c8194373f5ca" class="bulleted-list"><li style="list-style-type:circle">Warning: #FF9100 (Orange)</li></ul><ul id="2352881f-86f1-805b-843d-e1d829419c05" class="bulleted-list"><li style="list-style-type:circle">Offline: #BDBDBD (Grey)</li></ul></li></ul><ul id="2352881f-86f1-8038-a261-c5662e57af63" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip:</strong> “Connection: Online / Warning / Offline”</li></ul><ul id="2352881f-86f1-8071-8505-c1bf1500efed" class="bulleted-list"><li style="list-style-type:disc"><strong>Accessibility:</strong> ARIA role=“status”; screen reader announced on state change</li></ul><ul id="2352881f-86f1-80c3-bbb7-c79db6ffa8a7" class="bulleted-list"><li style="list-style-type:disc"><strong>Responsive Behavior:</strong> Always visible, shifts position if other elements expand</li></ul><hr id="2352881f-86f1-800e-a9aa-ecce0e3720e1"/><h3 id="2352881f-86f1-803b-8957-f1e4f84f777b" class=""><strong>4. Grid Toggle</strong></h3><p id="2352881f-86f1-80c9-9340-fd77836309aa" class=""><strong>A. Naming &amp; Iconography</strong></p><ul id="2352881f-86f1-8057-ac75-c13e91364795" class="bulleted-list"><li style="list-style-type:disc"><strong>Expert Name:</strong> Grid Toggle</li></ul><ul id="2352881f-86f1-804f-bcb9-d0e3321aa171" class="bulleted-list"><li style="list-style-type:disc"><strong>UI Label (ARIA/Tooltip):</strong> “Toggle grid overlay”</li></ul><ul id="2352881f-86f1-804d-8976-e6412a081fdd" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong> Minimalist 3x3 square grid (Apple/Adobe/Figma style)<ul id="2352881f-86f1-8041-9fa7-f4d21dc19c07" class="bulleted-list"><li style="list-style-type:circle"><strong>Unicode fallback:</strong> ◩ or SVG grid</li></ul></li></ul><ul id="2352881f-86f1-80c4-ab00-f2a37551b6c4" class="bulleted-list"><li style="list-style-type:disc"><strong>Placement:</strong> Immediately right of Connection Status on status bar</li></ul><p id="2352881f-86f1-8060-a065-cb594d958fc6" class=""><strong>B. UI States</strong></p><ul id="2352881f-86f1-8027-9779-e0af2f57a58f" class="bulleted-list"><li style="list-style-type:disc"><strong>On:</strong><ul id="2352881f-86f1-8003-a016-f67ed7411b8e" class="bulleted-list"><li style="list-style-type:circle">Icon is filled (white fill, #222 border), subtle blue border or background glow (e.g., #90CAF9 at 25% opacity)</li></ul><ul id="2352881f-86f1-808b-94a3-ea87afd4971c" class="bulleted-list"><li style="list-style-type:circle">Tooltip: “Hide grid overlay”</li></ul></li></ul><ul id="2352881f-86f1-80b5-8c0e-e568d37e6650" class="bulleted-list"><li style="list-style-type:disc"><strong>Off:</strong><ul id="2352881f-86f1-8090-9828-ee21f4f9d434" class="bulleted-list"><li style="list-style-type:circle">Icon outlined only (no fill), normal border (#BDBDBD)</li></ul><ul id="2352881f-86f1-8099-8c81-d37cd95d39a9" class="bulleted-list"><li style="list-style-type:circle">Tooltip: “Show grid overlay”</li></ul></li></ul><p id="2352881f-86f1-8063-ac62-eedcaa4648c5" class=""><strong>C. Behavior</strong></p><ul id="2352881f-86f1-80bd-8c6e-fee5544ad8e4" class="bulleted-list"><li style="list-style-type:disc"><strong>Click:</strong> Toggles overlay gridlines (aligned with 3D canvas plane, default grid spacing set in project or settings panel)</li></ul><ul id="2352881f-86f1-80ed-8eb9-f8b759799083" class="bulleted-list"><li style="list-style-type:disc"><strong>State Persistence:</strong> Remembers state for user/session (restores on reload)</li></ul><ul id="2352881f-86f1-80c1-bad0-cb3faab33ac3" class="bulleted-list"><li style="list-style-type:disc"><strong>Animation:</strong> Grid fades in/out (200 ms fade), icon animates subtle glow when toggled on</li></ul><ul id="2352881f-86f1-8088-b81f-f36bc1ede9fe" class="bulleted-list"><li style="list-style-type:disc"><strong>Feedback:</strong> ARIA live region announces “Grid overlay enabled/disabled”</li></ul><ul id="2352881f-86f1-80e3-aca5-db9d5a3be74f" class="bulleted-list"><li style="list-style-type:disc"><strong>Tab Order:</strong> Tab to select, space/enter toggles</li></ul><p id="2352881f-86f1-80fe-8a3b-ea81e2c7c8bb" class=""><strong>D. Accessibility</strong></p><ul id="2352881f-86f1-80ad-9d45-cad83cbc390d" class="bulleted-list"><li style="list-style-type:disc"><strong>ARIA role:</strong> <code>role=&quot;switch&quot; aria-checked=&quot;true|false&quot;</code></li></ul><ul id="2352881f-86f1-805f-84f7-d70b5547c309" class="bulleted-list"><li style="list-style-type:disc"><strong>Label:</strong> “Toggle grid overlay”</li></ul><ul id="2352881f-86f1-807c-bad8-d02d1e2afd3f" class="bulleted-list"><li style="list-style-type:disc"><strong>Keyboard:</strong> Full tab/space/enter support</li></ul><p id="2352881f-86f1-80f6-a856-f10e13a0e3c3" class=""><strong>E. Rationale</strong></p><ul id="2352881f-86f1-8076-b3a7-c3e95dbdf6cb" class="bulleted-list"><li style="list-style-type:disc"><strong>Why:</strong> Engineers and designers benefit from visual alignment aids when drawing ducts, equipment, or reference geometry.</li></ul><ul id="2352881f-86f1-80d3-860e-c846b4156257" class="bulleted-list"><li style="list-style-type:disc"><strong>Minimal icon:</strong> Recognized instantly in CAD/BIM/3D software.</li></ul><ul id="2352881f-86f1-80d8-82b9-dfe82163d337" class="bulleted-list"><li style="list-style-type:disc"><strong>Never occludes content:</strong> Grid is faint, always below drawn elements, color-contrasted for both light/dark canvas modes.</li></ul><p id="2352881f-86f1-8086-a760-dfb070949a82" class=""><strong>F. Visual Spec</strong></p><ul id="2352881f-86f1-80a2-a3c5-c53e9aabb41c" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon On:</strong><ul id="2352881f-86f1-802e-8fd8-dabbe810bd3d" class="bulleted-list"><li style="list-style-type:circle">White squares (#FFFFFF) with faint blue border (#90CAF9)</li></ul><ul id="2352881f-86f1-8007-8db5-d1f4ef924312" class="bulleted-list"><li style="list-style-type:circle">24×24 px, touch target 40×40 px</li></ul><ul id="2352881f-86f1-80e8-98bc-eb9668dfc0d2" class="bulleted-list"><li style="list-style-type:circle">Blue glow or background fill for active</li></ul></li></ul><ul id="2352881f-86f1-80b1-a7c7-c8ab6e081f98" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon Off:</strong><ul id="2352881f-86f1-80f8-b1c5-c2e7af02167d" class="bulleted-list"><li style="list-style-type:circle">No fill, border only (#BDBDBD)</li></ul></li></ul><hr id="2352881f-86f1-8000-ae80-f2cac83c5358"/><h3 id="2352881f-86f1-80ac-8133-dd489001ee5d" class=""><strong>5. Warning Highlight Toggle</strong> <em>(“Warning Glow”)</em></h3><p id="2352881f-86f1-807f-aab9-deb6cdb07aa8" class=""><strong>A. Naming &amp; Iconography</strong></p><ul id="2352881f-86f1-801b-bf64-e6833c57042d" class="bulleted-list"><li style="list-style-type:disc"><strong>Expert Name:</strong> Warning Highlight Toggle</li></ul><ul id="2352881f-86f1-8052-a60f-f607cd9da63f" class="bulleted-list"><li style="list-style-type:disc"><strong>UI Label (ARIA/Tooltip):</strong> “Toggle warning glow”</li></ul><ul id="2352881f-86f1-804d-8f9b-f2960fee93c8" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong> Triangle warning icon (⚠️ or custom SVG with subtle drop shadow)</li></ul><ul id="2352881f-86f1-8049-b51d-de49cd30a3c5" class="bulleted-list"><li style="list-style-type:disc"><strong>Placement:</strong> Right of Grid Toggle, before Model Summary</li></ul><p id="2352881f-86f1-807b-9736-ee7e28724d4d" class=""><strong>B. UI States</strong></p><ul id="2352881f-86f1-8049-a7c6-d0d3f202d057" class="bulleted-list"><li style="list-style-type:disc"><strong>On:</strong><ul id="2352881f-86f1-800b-951c-ca7df08a3475" class="bulleted-list"><li style="list-style-type:circle">Icon glows yellow (#FFEB3B at 60% opacity), possibly animated pulse</li></ul><ul id="2352881f-86f1-8008-af64-cf9aad2b00d5" class="bulleted-list"><li style="list-style-type:circle">Tooltip: “Highlight all ducts with warnings”</li></ul></li></ul><ul id="2352881f-86f1-804d-8449-f656cee590b4" class="bulleted-list"><li style="list-style-type:disc"><strong>Off:</strong><ul id="2352881f-86f1-807f-ac00-eb78f9aacdfa" class="bulleted-list"><li style="list-style-type:circle">Icon outlined, no fill, standard border (#BDBDBD)</li></ul><ul id="2352881f-86f1-8087-96c5-d7e1002bb721" class="bulleted-list"><li style="list-style-type:circle">Tooltip: “Disable warning highlight”</li></ul></li></ul><p id="2352881f-86f1-80c5-8db5-f0a932ae35d5" class=""><strong>C. Behavior</strong></p><ul id="2352881f-86f1-80c3-a01c-fcc6b77cc145" class="bulleted-list"><li style="list-style-type:disc"><strong>Click:</strong><ul id="2352881f-86f1-805b-8121-d64635556c47" class="bulleted-list"><li style="list-style-type:circle"><strong>On:</strong> All duct elements or nodes with active warnings glow with a colored outline/glow (red/yellow as appropriate for severity)</li></ul><ul id="2352881f-86f1-80f1-a21a-df9279f7f199" class="bulleted-list"><li style="list-style-type:circle"><strong>Off:</strong> No additional glow applied to ductwork, only base colors visible</li></ul></li></ul><ul id="2352881f-86f1-8004-9531-db55e995d176" class="bulleted-list"><li style="list-style-type:disc"><strong>Live Feedback:</strong><ul id="2352881f-86f1-80d9-8ba9-d33c34c8c4da" class="bulleted-list"><li style="list-style-type:circle">As warnings resolve/appear, affected ducts animate their glow in/out (200 ms)</li></ul></li></ul><ul id="2352881f-86f1-80fe-910b-cca3fb74ae48" class="bulleted-list"><li style="list-style-type:disc"><strong>Animation:</strong> Subtle pulsing glow for critical (red), soft glow for caution (yellow)</li></ul><ul id="2352881f-86f1-80c1-a4eb-de4a3f0651fd" class="bulleted-list"><li style="list-style-type:disc"><strong>Persistence:</strong> State retained for session; restores on reload</li></ul><ul id="2352881f-86f1-80c2-9b92-f1848a179f73" class="bulleted-list"><li style="list-style-type:disc"><strong>Feedback:</strong> ARIA region announces “Warning highlight enabled/disabled”</li></ul><ul id="2352881f-86f1-8073-86e2-ef08f28b9120" class="bulleted-list"><li style="list-style-type:disc"><strong>Tab Order:</strong> Tab/space/enter control</li></ul><p id="2352881f-86f1-80ff-b8b1-c75488aba353" class=""><strong>D. Accessibility</strong></p><ul id="2352881f-86f1-8063-b48e-f50a1250fc93" class="bulleted-list"><li style="list-style-type:disc"><strong>ARIA role:</strong> <code>role=&quot;switch&quot; aria-checked=&quot;true|false&quot;</code></li></ul><ul id="2352881f-86f1-8080-b609-c60183fb29b3" class="bulleted-list"><li style="list-style-type:disc"><strong>Label:</strong> “Toggle warning highlight on ducts”</li></ul><ul id="2352881f-86f1-8027-a8ff-ce5310902619" class="bulleted-list"><li style="list-style-type:disc"><strong>Keyboard:</strong> Tab/space/enter fully supported</li></ul><p id="2352881f-86f1-80ec-b3e2-ea86d6085bd1" class=""><strong>E. Rationale</strong></p><ul id="2352881f-86f1-80bc-9e16-c9ecdd68d27a" class="bulleted-list"><li style="list-style-type:disc"><strong>Why:</strong> Instantly visualizes compliance/risk areas without opening Model Summary or popovers; reduces missed warnings.</li></ul><ul id="2352881f-86f1-8030-84f0-d6d35487d73b" class="bulleted-list"><li style="list-style-type:disc"><strong>Best Practice:</strong> Engineers expect direct error highlighting—this toggle streamlines QA/validation during rapid design/edits.</li></ul><p id="2352881f-86f1-8058-9eb1-cc2e8c86e387" class=""><strong>F. Visual Spec</strong></p><ul id="2352881f-86f1-80c0-9572-c0811eaca956" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon On:</strong><ul id="2352881f-86f1-8009-9057-dfd7d3f74224" class="bulleted-list"><li style="list-style-type:circle">Triangle filled yellow (#FFEB3B), faint yellow glow or pulse around icon</li></ul><ul id="2352881f-86f1-8039-afb7-c9dc0f595b0b" class="bulleted-list"><li style="list-style-type:circle">24×24 px, 40×40 px touch area</li></ul></li></ul><ul id="2352881f-86f1-80e5-be8e-fddb54ab594e" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon Off:</strong><ul id="2352881f-86f1-8001-a8f3-de2fb44a7faf" class="bulleted-list"><li style="list-style-type:circle">Outlined triangle, no fill, grey border (#BDBDBD)</li></ul></li></ul><ul id="2352881f-86f1-8074-91fa-d61a01be791c" class="bulleted-list"><li style="list-style-type:disc"><strong>Glow on Model:</strong><ul id="2352881f-86f1-8063-b654-d4578ce9d3ff" class="bulleted-list"><li style="list-style-type:circle">Ducts: Shadow/glow color matches warning (red for critical, yellow for caution), 4–8 px soft outer glow</li></ul><ul id="2352881f-86f1-8094-9acf-c3778dd0b527" class="bulleted-list"><li style="list-style-type:circle">Animation: 200 ms fade in/out, pulse for new critical warnings</li></ul></li></ul><hr id="2352881f-86f1-8001-83aa-d60f6d666e1b"/><h3 id="2352881f-86f1-8085-9bf5-d0ea1d75f9a9" class=""><strong>6. Model Summary</strong></h3><ul id="2352881f-86f1-808c-bfef-e2a999df5047" class="bulleted-list"><li style="list-style-type:disc"><strong>Expert Name:</strong> Model Summary</li></ul><ul id="2352881f-86f1-80cc-b1cf-f47e6cd27884" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong> ▤ (dashboard/summary bars)</li></ul><ul id="2352881f-86f1-8027-842f-c1381325a834" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Button (with active/focused state)</li></ul><ul id="2352881f-86f1-802a-8601-e5014df6d09b" class="bulleted-list"><li style="list-style-type:disc"><strong>Function:</strong><ul id="2352881f-86f1-806e-9f0b-cfd0d46d88df" class="bulleted-list"><li style="list-style-type:circle">Opens Model Summary dock/panel (system metrics, warnings, code compliance)</li></ul><ul id="2352881f-86f1-80e9-b698-cf5b88f37b83" class="bulleted-list"><li style="list-style-type:circle">Includes: Live results by system, selection for systems, copy button, warning summary, jump-to-error, tips, compliance sources (SMACNA, ASHRAE, UL, user inputted local codes)</li></ul><ul id="2352881f-86f1-807f-b308-fb322251ca36" class="bulleted-list"><li style="list-style-type:circle">Color-coded warning badges: Red (critical), Yellow (caution), Grey (info)</li></ul><ul id="2352881f-86f1-8066-be43-e622616f0562" class="bulleted-list"><li style="list-style-type:circle">Panel appears above status bar, never blocks bar or main canvas</li></ul></li></ul><ul id="2352881f-86f1-808b-bce8-d809e93e9be6" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip:</strong> “Show model summary and compliance results”</li></ul><ul id="2352881f-86f1-80ba-8e9a-c83e399ce18e" class="bulleted-list"><li style="list-style-type:disc"><strong>Accessibility:</strong> ARIA role=“button”; labeled; keyboard and focusable</li></ul><ul id="2352881f-86f1-80ff-a509-e0416fa58fbc" class="bulleted-list"><li style="list-style-type:disc"><strong>Responsive Behavior:</strong> Button shrinks to icon-only on smaller widths</li></ul><hr id="2352881f-86f1-804d-b88d-dbf412adc8a5"/><h3 id="2352881f-86f1-80b3-8b79-e7126feaf9d2" class=""><strong>7. Quick Search</strong></h3><ul id="2352881f-86f1-8021-b0c6-ceb396c8b96e" class="bulleted-list"><li style="list-style-type:disc"><strong>Expert Name:</strong> Quick Search</li></ul><ul id="2352881f-86f1-801c-8ed8-feec2ac61173" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong> 🔍 (magnifying glass, pill-style input)</li></ul><ul id="2352881f-86f1-808e-86c5-c27188e658cd" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Expanding input</li></ul><ul id="2352881f-86f1-80b9-a095-e50c47b1098d" class="bulleted-list"><li style="list-style-type:disc"><strong>Function:</strong><ul id="2352881f-86f1-80b1-a522-c83d73dfac26" class="bulleted-list"><li style="list-style-type:circle">Click or press <code>/</code> to expand for searching tips, help, or docs</li></ul><ul id="2352881f-86f1-8025-92f7-c6a4b3e094ff" class="bulleted-list"><li style="list-style-type:circle">Suggestions show live as user types</li></ul><ul id="2352881f-86f1-80d5-8e7f-cf8a10c2f53e" class="bulleted-list"><li style="list-style-type:circle">Results accessible with keyboard</li></ul></li></ul><ul id="2352881f-86f1-800d-b02f-fda1cd695b6b" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip:</strong> “Search help, tips, and documentation”</li></ul><ul id="2352881f-86f1-802d-95e5-d2c325c9cc90" class="bulleted-list"><li style="list-style-type:disc"><strong>Accessibility:</strong> ARIA role=“search”; live region; keyboard navigation; tab order</li></ul><ul id="2352881f-86f1-8057-9f83-deccb7d1ab3e" class="bulleted-list"><li style="list-style-type:disc"><strong>Responsive Behavior:</strong> Collapses to icon on bar width reduction, but expands smoothly when used; all elements slide to accommodate</li></ul><hr id="2352881f-86f1-80e4-82ef-ccaedb8aded5"/><h3 id="2352881f-86f1-809b-97f4-c92648fd10fc" class=""><strong>8. Units Selector</strong></h3><ul id="2352881f-86f1-8006-9dd1-fff0cf5c44c0" class="bulleted-list"><li style="list-style-type:disc"><strong>Expert Name:</strong> Units Selector</li></ul><ul id="2352881f-86f1-80fd-8fba-c50a64e13d1f" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon:</strong> Dropdown with label (“Imperial ▼” or “SI ▼”); ruler icon optional</li></ul><ul id="2352881f-86f1-803d-a0c3-d5ce50937fa0" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Dropdown menu</li></ul><ul id="2352881f-86f1-8075-9456-c32f0e09bc2b" class="bulleted-list"><li style="list-style-type:disc"><strong>Function:</strong><ul id="2352881f-86f1-801b-b0fa-e703b6a38bfa" class="bulleted-list"><li style="list-style-type:circle">Click to switch global units (Imperial/SI/custom)</li></ul><ul id="2352881f-86f1-8026-b648-c777daae3842" class="bulleted-list"><li style="list-style-type:circle">Updates all tool fields and results instantly</li></ul></li></ul><ul id="2352881f-86f1-801d-8eb5-e21959d4cc21" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip:</strong> “Change units: Imperial, SI, …”</li></ul><ul id="2352881f-86f1-80d5-bc34-e89a594da2d9" class="bulleted-list"><li style="list-style-type:disc"><strong>Accessibility:</strong> ARIA role=“combobox”; keyboard navigable</li></ul><ul id="2352881f-86f1-8072-855d-d87e76b4dd64" class="bulleted-list"><li style="list-style-type:disc"><strong>Responsive Behavior:</strong> Text may shorten to icon or initials (“in”, “mm”) as bar narrows</li></ul><hr id="2352881f-86f1-8082-8914-d0a0a7191512"/><h2 id="2352881f-86f1-80fc-afdb-f4964445f6ff" class=""><strong>Dynamic Behavior &amp; Responsiveness</strong></h2><ul id="2352881f-86f1-80ad-bc50-cd5e8cceb964" class="bulleted-list"><li style="list-style-type:disc"><strong>Single-line guarantee:</strong> All elements scale, contract, or show only icons as needed, but <strong>never stack or wrap.</strong></li></ul><ul id="2352881f-86f1-8025-89f2-dd17f38c0b97" class="bulleted-list"><li style="list-style-type:disc"><strong>Expanding elements (e.g., Search, Model Summary):</strong><ul id="2352881f-86f1-8083-ad1e-e282273517c2" class="bulleted-list"><li style="list-style-type:circle">When expanded, adjacent controls shift smoothly to accommodate width change (with minimum/maximum bounds to prevent overlap).</li></ul><ul id="2352881f-86f1-8000-b891-e8a0770ec03f" class="bulleted-list"><li style="list-style-type:circle">If multiple elements are expanded, priority order is maintained (core state controls always visible).</li></ul></li></ul><ul id="2352881f-86f1-8095-ad19-d102fe0c3c04" class="bulleted-list"><li style="list-style-type:disc"><strong>Consistent tab order and ARIA live region announcements</strong></li></ul><ul id="2352881f-86f1-8022-a3f7-c27d0cedc1b5" class="bulleted-list"><li style="list-style-type:disc"><strong>Panel/flyouts always open above bar, never occlude bar or main canvas</strong></li></ul><hr id="2352881f-86f1-80aa-8387-dd182bc70dd6"/><h2 id="2352881f-86f1-803e-aad2-c17a591b155b" class=""><strong>Visual Design &amp; Color</strong></h2><ul id="2352881f-86f1-80ac-a4e6-d8855ddb7201" class="bulleted-list"><li style="list-style-type:disc"><strong>Background:</strong> White (#FFFFFF) with 92% opacity (frosted glass blur effect)</li></ul><ul id="2352881f-86f1-806e-8c27-c93ab1da9e73" class="bulleted-list"><li style="list-style-type:disc"><strong>Border:</strong> Subtle “liquid glass” (#E0E0E0, 18% opacity)</li></ul><ul id="2352881f-86f1-8024-950f-c911de66314a" class="bulleted-list"><li style="list-style-type:disc"><strong>Text &amp; Icons:</strong> Charcoal (#222222) for highest contrast</li></ul><ul id="2352881f-86f1-808a-9518-e2bdff78761f" class="bulleted-list"><li style="list-style-type:disc"><strong>Active/Focus States:</strong><ul id="2352881f-86f1-802a-b760-c0ded02d7fa7" class="bulleted-list"><li style="list-style-type:circle">Slightly increased border/blur on hover/focus</li></ul><ul id="2352881f-86f1-80d1-85c4-c1b372682cb3" class="bulleted-list"><li style="list-style-type:circle">Model Summary shows a faint accent glow when open</li></ul><ul id="2352881f-86f1-8074-a659-d6a8743d2706" class="bulleted-list"><li style="list-style-type:circle">Quick Search expands with gentle animation</li></ul></li></ul><ul id="2352881f-86f1-8040-a38a-f7710274dd1a" class="bulleted-list"><li style="list-style-type:disc"><strong>Color cues for status:</strong><ul id="2352881f-86f1-80e7-9dba-e2ce5bdce947" class="bulleted-list"><li style="list-style-type:circle">Connection: Green / Orange / Grey</li></ul><ul id="2352881f-86f1-8098-bd03-ee4d748f1d8e" class="bulleted-list"><li style="list-style-type:circle">Save: Green (saved), Orange (saving), Red (error)</li></ul><ul id="2352881f-86f1-80e4-8ddd-dde76dc2768c" class="bulleted-list"><li style="list-style-type:circle">Warnings: As above (Red/Yellow/Grey)</li></ul></li></ul><hr id="2352881f-86f1-80d5-b697-fa7042359d01"/><h2 id="2352881f-86f1-80a0-b55d-e8b422d1e349" class=""><strong>Accessibility</strong></h2><ul id="2352881f-86f1-80f7-b69f-c1402b115d87" class="bulleted-list"><li style="list-style-type:disc"><strong>Keyboard:</strong><ul id="2352881f-86f1-80a2-a2f4-cff541308920" class="bulleted-list"><li style="list-style-type:circle">All controls accessible and focusable in tab order left ➔ right</li></ul><ul id="2352881f-86f1-804c-a1fe-eb2e0fce2cc3" class="bulleted-list"><li style="list-style-type:circle">Quick Search can be focused with <code>/</code></li></ul><ul id="2352881f-86f1-80f1-abc6-e4f43c701223" class="bulleted-list"><li style="list-style-type:circle">Dropdowns navigable via arrows/enter</li></ul></li></ul><ul id="2352881f-86f1-80f6-b5d1-e26d0eb33166" class="bulleted-list"><li style="list-style-type:disc"><strong>Screen Readers:</strong><ul id="2352881f-86f1-807a-acb5-f8fe43b98c95" class="bulleted-list"><li style="list-style-type:circle">Descriptive ARIA labels on all elements</li></ul><ul id="2352881f-86f1-8063-9f65-e94156fcf8c3" class="bulleted-list"><li style="list-style-type:circle">State changes announced live (e.g., connection change, save status)</li></ul></li></ul><ul id="2352881f-86f1-8010-a0d0-fee3b7e65482" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltips:</strong><ul id="2352881f-86f1-80d3-961d-c084067cd2c7" class="bulleted-list"><li style="list-style-type:circle">On hover/focus, descriptive tooltips for every element</li></ul></li></ul><hr id="2352881f-86f1-8034-b2fe-cb607b991e3e"/><h2 id="2352881f-86f1-8092-aa3d-fe89071b8a49" class=""><strong>Rationale &amp; Best Practice Alignment</strong></h2><ul id="2352881f-86f1-804e-9a0a-ea77bcb32e23" class="bulleted-list"><li style="list-style-type:disc"><strong>Professional conventions:</strong><ul id="2352881f-86f1-80dd-9364-e505ace077ca" class="bulleted-list"><li style="list-style-type:circle">Single-line status bar mirrors Figma, Adobe, macOS, CAD suites for engineering clarity</li></ul><ul id="2352881f-86f1-8038-b756-c59eea4601af" class="bulleted-list"><li style="list-style-type:circle">Left-to-right priority: file/actions → save/state → output/metrics → search → units</li></ul></li></ul><ul id="2352881f-86f1-802e-9c3f-d037145bab73" class="bulleted-list"><li style="list-style-type:disc"><strong>No navigation, drawing, or view controls</strong>: Only workspace state and context actions.</li></ul><ul id="2352881f-86f1-8024-8499-deb4e552b441" class="bulleted-list"><li style="list-style-type:disc"><strong>No notification center unless future need arises</strong></li></ul><ul id="2352881f-86f1-80e5-a4de-e668f31cb8f5" class="bulleted-list"><li style="list-style-type:disc"><strong>Dynamic expansion:</strong> Elements adjust seamlessly—never covering or wrapping the bar.</li></ul><hr id="2352881f-86f1-8040-b5c2-e8e35d4b08ef"/><p id="2352881f-86f1-80c3-9504-f1aa5db5c18d" class=""><strong>This documentation is the authoritative reference for the Status Bar in the Air Duct Sizer 3D Tool, meeting all requirements for professional-grade, high-performance engineering applications.</strong></p></details></li></ul><hr id="2332881f-86f1-80eb-a284-efbfb6c4cc66"/><ul id="2332881f-86f1-8057-a6f9-dffa3a56b003" class="toggle"><li><details open=""><summary><strong>8. View Cube</strong></summary><h1 id="2352881f-86f1-805f-aea6-dddfb4035ca5" class=""><strong>Air Duct Sizer 3D Tool — View Cube Specification (Final)</strong></h1><hr id="2352881f-86f1-801b-ab11-d594d138e50f"/><h2 id="2352881f-86f1-80fa-b101-f3349fc72567" class=""><strong>1. Naming</strong></h2><ul id="2352881f-86f1-802a-8d41-f5f4bfb28689" class="bulleted-list"><li style="list-style-type:disc"><strong>Element Name:</strong> View Cube</li></ul><ul id="2352881f-86f1-8043-ad82-c3de08e3019f" class="bulleted-list"><li style="list-style-type:disc"><strong>Expert Rationale:</strong><p id="2352881f-86f1-804e-859e-ffae35ba4098" class="">The term &quot;View Cube&quot; is universally recognized in 3D CAD, BIM, and design tools (e.g., Autodesk, SketchUp, Revit) as a standard for orientation control, maximizing clarity for engineers and advanced users.</p></li></ul><hr id="2352881f-86f1-804c-a9a4-e5a297b42972"/><h2 id="2352881f-86f1-8079-b0c9-ce96e3c2bf61" class=""><strong>2. Placement</strong></h2><ul id="2352881f-86f1-8013-8f01-f6cbdce56940" class="bulleted-list"><li style="list-style-type:disc"><strong>Location:</strong><p id="2352881f-86f1-800f-a3bf-f7bfa37c377c" class=""><strong>Top-right corner</strong> of the 3D workspace canvas.</p><ul id="2352881f-86f1-800e-8486-d8329e596e86" class="bulleted-list"><li style="list-style-type:circle">Always overlays the 3D canvas (not docked to any panel).</li></ul><ul id="2352881f-86f1-80e8-a3bc-f052e2a2fae7" class="bulleted-list"><li style="list-style-type:circle">Floating margin (suggested: 24 px from top/right edge).</li></ul><ul id="2352881f-86f1-8006-8853-df1a413efb9a" class="bulleted-list"><li style="list-style-type:circle">Never overlaps persistent UI panels (context panels, status bar).</li></ul></li></ul><hr id="2352881f-86f1-80e1-a35e-cc5a62136fb9"/><h2 id="2352881f-86f1-8036-9c37-dc3973f36907" class=""><strong>3. Appearance &amp; Design</strong></h2><ul id="2352881f-86f1-80ad-845d-ee440012d3df" class="bulleted-list"><li style="list-style-type:disc"><strong>Form:</strong><ul id="2352881f-86f1-8042-afb6-f568f746c068" class="bulleted-list"><li style="list-style-type:circle">3D cube (or, optionally, rounded cuboid) with labeled faces.</li></ul><ul id="2352881f-86f1-806f-a672-d576c5be30a8" class="bulleted-list"><li style="list-style-type:circle">Size: 56 × 56 px (standard; scalable for accessibility).</li></ul></li></ul><ul id="2352881f-86f1-807e-b078-cd17cda0364b" class="bulleted-list"><li style="list-style-type:disc"><strong>Face Labels:</strong><ul id="2352881f-86f1-803f-8030-c544ede5cd8f" class="bulleted-list"><li style="list-style-type:circle">“Top”, “Front”, “Right”, “Left”, “Back”, “Bottom” (at least Top/Front/Right always visible)</li></ul><ul id="2352881f-86f1-80ce-aa88-f8dd7997353b" class="bulleted-list"><li style="list-style-type:circle">Isometric edge or corner visually emphasized.</li></ul></li></ul><ul id="2352881f-86f1-803a-b038-fe92f4b8783b" class="bulleted-list"><li style="list-style-type:disc"><strong>Color:</strong><ul id="2352881f-86f1-804f-8a3e-c9c4c878a63e" class="bulleted-list"><li style="list-style-type:circle"><strong>Faces:</strong> Light glassy white (#F9FAFB, 90% opacity) with subtle shadow</li></ul><ul id="2352881f-86f1-80e6-a3b4-e5d65b1b4476" class="bulleted-list"><li style="list-style-type:circle"><strong>Labels:</strong> Deep grey (#222222), bold, always visible on all backgrounds</li></ul><ul id="2352881f-86f1-802b-b5e0-ce038c6a446e" class="bulleted-list"><li style="list-style-type:circle"><strong>Outline:</strong> Liquid glass border (#E0E0E0, 25% opacity)</li></ul></li></ul><ul id="2352881f-86f1-80e6-acd3-ffd80c437185" class="bulleted-list"><li style="list-style-type:disc"><strong>Opacity:</strong><ul id="2352881f-86f1-8093-bf02-e54f662bc1e3" class="bulleted-list"><li style="list-style-type:circle"><strong>Default:</strong> 50%</li></ul><ul id="2352881f-86f1-80fd-a85b-eb4de6468af7" class="bulleted-list"><li style="list-style-type:circle"><strong>On hover/focus:</strong> 100%</li></ul><ul id="2352881f-86f1-8022-8d0c-eeec5d461bdd" class="bulleted-list"><li style="list-style-type:circle"><strong>On drag/interaction:</strong> 100%</li></ul></li></ul><ul id="2352881f-86f1-8021-ae7e-c8de22f0dd67" class="bulleted-list"><li style="list-style-type:disc"><strong>Interaction Cursor:</strong><ul id="2352881f-86f1-8058-834e-eed3ab34275d" class="bulleted-list"><li style="list-style-type:circle">Default: pointer/hand</li></ul><ul id="2352881f-86f1-8080-8592-ff16b60aa452" class="bulleted-list"><li style="list-style-type:circle">On hover: faces highlight with faint color (e.g., #FF9800 for current, #90CAF9 for hover)</li></ul></li></ul><hr id="2352881f-86f1-8012-8969-cd5c0d37321c"/><h2 id="2352881f-86f1-8032-9724-d3a9541af4f8" class=""><strong>4. Behavior &amp; Functionality</strong></h2><ul id="2352881f-86f1-80ec-a77b-e13c01b74bb0" class="bulleted-list"><li style="list-style-type:disc"><strong>Action:</strong><ul id="2352881f-86f1-807c-9415-f61dfd9ca734" class="bulleted-list"><li style="list-style-type:circle"><strong>Clicking a face:</strong> Instantly rotates the camera/view to the respective orthogonal view (Top, Front, Right, etc.)</li></ul><ul id="2352881f-86f1-802d-9997-d82f1f12d793" class="bulleted-list"><li style="list-style-type:circle"><strong>Clicking an edge/corner:</strong> Rotates to nearest isometric/perspective view</li></ul></li></ul><ul id="2352881f-86f1-803b-bfd7-eb1b48ab392b" class="bulleted-list"><li style="list-style-type:disc"><strong>Animated Feedback:</strong><ul id="2352881f-86f1-80b8-b1b2-f304f394516f" class="bulleted-list"><li style="list-style-type:circle">Smooth 3D animation rotates the model and the View Cube in sync (≤300 ms, matches CAD standards)</li></ul></li></ul><ul id="2352881f-86f1-80d9-8aff-dd3096cccc2c" class="bulleted-list"><li style="list-style-type:disc"><strong>Focus:</strong><ul id="2352881f-86f1-801f-a298-e5197938fa80" class="bulleted-list"><li style="list-style-type:circle">Cube remains above main canvas and 3D content at all times.</li></ul><ul id="2352881f-86f1-800c-bf04-d9dfea3f9d3a" class="bulleted-list"><li style="list-style-type:circle">Never blocks interaction with selected objects or UI overlays (z-order is above 3D, below modal panels).</li></ul></li></ul><ul id="2352881f-86f1-80a4-bb71-e7be47dbb47e" class="bulleted-list"><li style="list-style-type:disc"><strong>Responsiveness:</strong><ul id="2352881f-86f1-8094-a5f1-ea47b560aa3e" class="bulleted-list"><li style="list-style-type:circle">Scales for high DPI, tablets, touchscreens (minimum 44×44 px tap target)</li></ul></li></ul><hr id="2352881f-86f1-80dd-a02f-c592da1c655d"/><h2 id="2352881f-86f1-80e8-8ee1-c59084ccbd43" class=""><strong>5. Accessibility</strong></h2><ul id="2352881f-86f1-8039-9f73-caf3993b33a0" class="bulleted-list"><li style="list-style-type:disc"><strong>Keyboard:</strong><ul id="2352881f-86f1-80b6-bacb-e69fb6ec475c" class="bulleted-list"><li style="list-style-type:circle">Tab-to-focus enabled (focus ring appears around cube)</li></ul><ul id="2352881f-86f1-801e-bd2c-d5a593005110" class="bulleted-list"><li style="list-style-type:circle">Arrows to move selection (cycles faces/edges)</li></ul><ul id="2352881f-86f1-80fc-8eec-dca04cf8902f" class="bulleted-list"><li style="list-style-type:circle">Enter/Space applies view change</li></ul></li></ul><ul id="2352881f-86f1-8074-8c77-cc678f7fecea" class="bulleted-list"><li style="list-style-type:disc"><strong>ARIA:</strong><ul id="2352881f-86f1-80ae-9cf8-ca8f37ab36b4" class="bulleted-list"><li style="list-style-type:circle">role=“toolbar”, labels for each face (“Set view: Top”, etc.)</li></ul><ul id="2352881f-86f1-80d1-955b-cb63d68cf0f5" class="bulleted-list"><li style="list-style-type:circle">Announce current view on change (“View set to: Right”)</li></ul></li></ul><ul id="2352881f-86f1-8097-9cc8-ff5fc9b081c9" class="bulleted-list"><li style="list-style-type:disc"><strong>Contrast:</strong><ul id="2352881f-86f1-8092-a00d-c8c066dcd4ad" class="bulleted-list"><li style="list-style-type:circle">Face label contrast ≥4.5:1 on all backgrounds</li></ul></li></ul><ul id="2352881f-86f1-80dd-9da8-e9a3c95ad7e8" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip:</strong><ul id="2352881f-86f1-8069-b547-e6710f6052d4" class="bulleted-list"><li style="list-style-type:circle">On hover/focus, tooltips such as: “Click to set view: Top”</li></ul></li></ul><hr id="2352881f-86f1-805a-a5bd-cc93ace3d5d2"/><h2 id="2352881f-86f1-8084-8f31-fe53bdc1b049" class=""><strong>6. Rationale</strong></h2><ul id="2352881f-86f1-803a-aada-fa06a3ea3b09" class="bulleted-list"><li style="list-style-type:disc"><strong>Instant spatial context:</strong> Engineers need to orient models quickly, especially when troubleshooting or aligning ductwork.</li></ul><ul id="2352881f-86f1-8093-961c-f26fa32a8e74" class="bulleted-list"><li style="list-style-type:disc"><strong>Universal UI convention:</strong> Mirrors industry leaders (Autodesk, BricsCAD, Fusion 360) for zero learning curve.</li></ul><ul id="2352881f-86f1-802d-8e6e-d33c09f39bdf" class="bulleted-list"><li style="list-style-type:disc"><strong>Unobtrusive:</strong> Default semi-transparent style ensures cube never obscures workspace or content, but is always available.</li></ul><ul id="2352881f-86f1-80a0-8a9a-e7960ce6dc6f" class="bulleted-list"><li style="list-style-type:disc"><strong>Keyboard/touch:</strong> Accessible for all users and devices, including mouse, touch, and keyboard workflows.</li></ul><hr id="2352881f-86f1-8007-852f-f11a34c63ec9"/><h2 id="2352881f-86f1-800e-9fd7-e16396a715ca" class=""><strong>7. Example Wireframe (ASCII)</strong></h2><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2352881f-86f1-8023-902e-d5988fbec89c" class="code"><code class="language-Plain Text">   ┌───────────────────────────── Air Duct Sizer 3D Canvas ─────────────────────────────┐
   │                                                                                   │
   │                                                      ┌───────┐                    │
   │                                                      │ Top   │  ◀── View Cube     │
   │                                                      │  ┌─┐  │                    │
   │                                                      │ F │R│ │                    │
   │                                                      └─┘─┘─┘                    │
   │                                                                                   │
   └───────────────────────────────────────────────────────────────────────────────────┘
</code></pre><ul id="2352881f-86f1-8019-917f-ccc0322b3925" class="bulleted-list"><li style="list-style-type:disc"><strong>Top-right corner, floats above 3D canvas</strong></li></ul><ul id="2352881f-86f1-809f-b7e6-e43bdb924e2f" class="bulleted-list"><li style="list-style-type:disc"><strong>Faces clearly labeled and visible</strong></li></ul><hr id="2352881f-86f1-8022-847d-d2e67e81070a"/><h2 id="2352881f-86f1-80f5-bbb4-da4d7028c3d9" class=""><strong>Color Codes</strong></h2><ul id="2352881f-86f1-80de-b27f-d2843149c908" class="bulleted-list"><li style="list-style-type:disc"><strong>Face Background:</strong> #F9FAFB, 90% opacity (rgba(249,250,251,0.9))</li></ul><ul id="2352881f-86f1-80fd-ad74-cd778c005f3e" class="bulleted-list"><li style="list-style-type:disc"><strong>Face Label/Text:</strong> #222222</li></ul><ul id="2352881f-86f1-8016-8b91-d3caabfd94f9" class="bulleted-list"><li style="list-style-type:disc"><strong>Active Face Hover:</strong> #FF9800 (Orange, only subtle)</li></ul><ul id="2352881f-86f1-809f-af59-d5a3546d876e" class="bulleted-list"><li style="list-style-type:disc"><strong>Cube Outline:</strong> #E0E0E0, 25% opacity (rgba(224,224,224,0.25))</li></ul><hr id="2352881f-86f1-8046-ac3d-e29d9f35b2a7"/><h2 id="2352881f-86f1-80ae-a026-d325cbce886c" class=""><strong>Exclusions</strong></h2><ul id="2352881f-86f1-806f-9654-fb08574a124a" class="bulleted-list"><li style="list-style-type:disc"><strong>Never docked to panel or moved by user</strong></li></ul><ul id="2352881f-86f1-8042-96a0-c007925e0c30" class="bulleted-list"><li style="list-style-type:disc"><strong>No custom view labels (unless user can configure)</strong></li></ul><ul id="2352881f-86f1-803c-8696-c8011dc4aeba" class="bulleted-list"><li style="list-style-type:disc"><strong>No animation &gt; 300 ms</strong></li></ul><hr id="2352881f-86f1-8065-8040-f12d4e807414"/><h2 id="2352881f-86f1-80db-8403-c24384ed23fb" class=""><strong>Summary Table</strong></h2><table id="2352881f-86f1-8069-a42b-f4592323c84e" class="simple-table"><thead class="simple-table-header"><tr id="2352881f-86f1-80e7-a0f1-da126e457595"><th id="=G|o" class="simple-table-header-color simple-table-header">Property</th><th id="G[dx" class="simple-table-header-color simple-table-header">Value</th></tr></thead><tbody><tr id="2352881f-86f1-8091-8068-d802c76c97df"><td id="=G|o" class="">Name</td><td id="G[dx" class="">View Cube</td></tr><tr id="2352881f-86f1-8085-804a-f1650cf47714"><td id="=G|o" class="">Placement</td><td id="G[dx" class="">Top-right 3D canvas (floating, never blocked)</td></tr><tr id="2352881f-86f1-804e-a397-fd2bf96c12bf"><td id="=G|o" class="">Size</td><td id="G[dx" class="">56×56 px (scalable, min 44×44 px for touch)</td></tr><tr id="2352881f-86f1-8059-9457-fdf47b6e116b"><td id="=G|o" class="">Faces</td><td id="G[dx" class="">Top, Front, Right, (others as needed)</td></tr><tr id="2352881f-86f1-8017-91c2-d76fe9a12c74"><td id="=G|o" class="">Opacity</td><td id="G[dx" class="">50% default, 100% hover/active</td></tr><tr id="2352881f-86f1-80ca-99ca-c0d9449716ce"><td id="=G|o" class="">Labels</td><td id="G[dx" class="">#222222 (deep grey)</td></tr><tr id="2352881f-86f1-8057-98eb-c230507295e8"><td id="=G|o" class="">Face BG</td><td id="G[dx" class="">#F9FAFB (white glass), 90% opacity</td></tr><tr id="2352881f-86f1-8014-81fd-fde5c80b4495"><td id="=G|o" class="">Border</td><td id="G[dx" class="">#E0E0E0, 25% opacity</td></tr><tr id="2352881f-86f1-8024-9b02-edd5fd5641c8"><td id="=G|o" class="">Accessibility</td><td id="G[dx" class="">Keyboard, ARIA, tooltips</td></tr></tbody></table></details></li></ul><hr id="2332881f-86f1-80df-90e4-df829827bbe8"/><ul id="2332881f-86f1-8029-8a31-d2a600f19acf" class="toggle"><li><details open=""><summary><strong>Interaction Workflow</strong></summary><ul id="2332881f-86f1-809c-80e5-c039ea18243e" class="bulleted-list"><li style="list-style-type:disc"><strong>Draw Phase:</strong><ul id="2332881f-86f1-80e3-9199-f516ecd2a56f" class="bulleted-list"><li style="list-style-type:circle">Toggle Pencil ON → draw stick lines (left-click); right-click to pan at any time</li></ul></li></ul><ul id="2332881f-86f1-80e1-b78f-ca72d1920059" class="bulleted-list"><li style="list-style-type:disc"><strong>Build Phase:</strong><ul id="2332881f-86f1-809f-b78e-ee3ee6c23ea3" class="bulleted-list"><li style="list-style-type:circle">Click <em>Build Ductwork</em> (on Results &amp; Warnings Bar) → generates 3D ducts</li></ul><ul id="2332881f-86f1-80bd-b5c1-ddef7f6c5bc6" class="bulleted-list"><li style="list-style-type:circle">Any issues appear immediately in the bar and warnings overlay</li></ul></li></ul><ul id="2332881f-86f1-8060-b5ea-d7d528970020" class="bulleted-list"><li style="list-style-type:disc"><strong>Edit/Inspect:</strong><ul id="2332881f-86f1-8004-a3ea-e5e28e0d36dd" class="bulleted-list"><li style="list-style-type:circle">Select duct/fitting → pop-up for properties</li></ul></li></ul><ul id="2332881f-86f1-80fb-971b-f2a28a96861d" class="bulleted-list"><li style="list-style-type:disc"><strong>Export/Status:</strong><ul id="2332881f-86f1-808a-bbb3-cbd36f0a905d" class="bulleted-list"><li style="list-style-type:circle">All file actions, calibration, and help accessed via Status Bar (bottom)</li></ul><ul id="2332881f-86f1-80b0-a49d-cfa73d990b0d" class="bulleted-list"><li style="list-style-type:circle">System notifications and health always visible</li></ul></li></ul></details></li></ul><hr id="2332881f-86f1-80f2-a7a9-cb225683a4d1"/></details></li></ul><ul id="2312881f-86f1-8012-a1b6-fa375c3edc08" class="toggle"><li><details open=""><summary><strong>Drawing Elements</strong></summary><ul id="2352881f-86f1-8088-ae43-f71ac296a90d" class="bulleted-list"><li style="list-style-type:disc"><strong>Element definitions:</strong> Ducts, Fittings, Equipment, Rooms</li></ul><ul id="2352881f-86f1-80ca-b65a-dcbff0474eda" class="bulleted-list"><li style="list-style-type:disc"><strong>Full canonical list of supported Fittings (with expansion placeholder)</strong></li></ul><ul id="2352881f-86f1-80cf-b7e6-d55ae4eeb097" class="bulleted-list"><li style="list-style-type:disc"><strong>How drawn lines behave (centerlines, snapping, editing, states)</strong></li></ul><ul id="2352881f-86f1-80ee-abcb-fd4604bf6f7d" class="bulleted-list"><li style="list-style-type:disc"><strong>Interaction/UX conventions</strong></li></ul><hr id="2352881f-86f1-8081-b9a3-fc2714c60ca8"/><h1 id="2352881f-86f1-8040-9b5a-e67adb3839b3" class=""><strong>Drawing Elements Specification — Air Duct Sizer 3D Tool</strong></h1><hr id="2352881f-86f1-8014-9bd4-dcedee7151c5"/><h2 id="2352881f-86f1-80c0-bbc4-f366b82b0f8f" class=""><strong>1. Element Definitions &amp; Roles</strong></h2><h3 id="2352881f-86f1-80c2-b5fb-d8e143a3825f" class=""><strong>A. Duct</strong></h3><ul id="2352881f-86f1-80ef-afca-f6b4fde434b3" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Linear segment (stick/centerline before extrusion)</li></ul><ul id="2352881f-86f1-80c0-a1a7-cc9355b3ec81" class="bulleted-list"><li style="list-style-type:disc"><strong>Purpose:</strong> Represents the main airflow path; becomes a 3D duct upon conversion.</li></ul><ul id="2352881f-86f1-80a9-9ada-cbece8d030fc" class="bulleted-list"><li style="list-style-type:disc"><strong>Behavior:</strong> Always starts/ends at a node; can be straight or multi-segmented (not freeform curves for this MVP).</li></ul><h3 id="2352881f-86f1-8023-9e64-d08886cc668e" class=""><strong>B. Fittings</strong></h3><ul id="2352881f-86f1-8036-9f92-e4e9cb08fa89" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Junction or directional change component</li></ul><ul id="2352881f-86f1-805e-b649-fe92c4d7ae35" class="bulleted-list"><li style="list-style-type:disc"><strong>Purpose:</strong> Connects duct segments, allows transitions, splits, or directional changes.</li></ul><ul id="2352881f-86f1-80f7-b993-d8a3e2c60831" class="bulleted-list"><li style="list-style-type:disc"><strong>Behavior:</strong> Auto-inserted at geometry nodes (e.g., intersection, angle) OR user-selected for manual override.</li></ul><h3 id="2352881f-86f1-8039-936e-dd63c6d8e0b6" class=""><strong>Canonical Fittings List (MVP)</strong></h3><p id="2352881f-86f1-806d-a8c0-c1af0b7a9626" class=""><em>(All auto-named on insertion; user can rename/edit properties after generation)</em></p><table id="2352881f-86f1-8007-807b-dff9cc7cad6d" class="simple-table"><thead class="simple-table-header"><tr id="2352881f-86f1-8092-8d67-efdbb46158e6"><th id="miu?" class="simple-table-header-color simple-table-header">Category</th><th id="URa=" class="simple-table-header-color simple-table-header">Fitting Type</th><th id="iYdH" class="simple-table-header-color simple-table-header">Typical Insertion Trigger</th><th id="=YEx" class="simple-table-header-color simple-table-header">Properties Editable?</th></tr></thead><tbody><tr id="2352881f-86f1-805a-b469-d05cf4cf2f6d"><td id="miu?" class="">Directional Change</td><td id="URa=" class="">Elbow (90°, 45°)</td><td id="iYdH" class="">Angle between segments</td><td id="=YEx" class="">Yes</td></tr><tr id="2352881f-86f1-800f-8676-e2ae64ab0d9c"><td id="miu?" class="">Junction</td><td id="URa=" class="">Tee</td><td id="iYdH" class="">3-way intersection</td><td id="=YEx" class="">Yes</td></tr><tr id="2352881f-86f1-8018-ba0f-e2b6b93246f1"><td id="miu?" class="">Junction</td><td id="URa=" class="">Cross</td><td id="iYdH" class="">4-way intersection</td><td id="=YEx" class="">Yes</td></tr><tr id="2352881f-86f1-80dc-bfa1-f519f91301cc"><td id="miu?" class="">Transition</td><td id="URa=" class="">Reducer/Enlarger</td><td id="iYdH" class="">Change in duct size</td><td id="=YEx" class="">Yes</td></tr><tr id="2352881f-86f1-8062-af10-c2b42ea370b1"><td id="miu?" class="">Transition</td><td id="URa=" class="">Offset</td><td id="iYdH" class="">Two parallel ducts offset</td><td id="=YEx" class="">Yes</td></tr><tr id="2352881f-86f1-80bb-9093-e94d3dbf0e5b"><td id="miu?" class="">Control</td><td id="URa=" class="">Damper</td><td id="iYdH" class="">Inserted at user-selected segment</td><td id="=YEx" class="">Yes</td></tr><tr id="2352881f-86f1-80cc-8efa-dda6934bafc1"><td id="miu?" class="">Terminal</td><td id="URa=" class="">End Cap</td><td id="iYdH" class="">Segment ends (not open to a room)</td><td id="=YEx" class="">Yes</td></tr><tr id="2352881f-86f1-8042-8318-f18141c1044f"><td id="miu?" class="">Specialty</td><td id="URa=" class="">Access Door</td><td id="iYdH" class="">User-inserted only</td><td id="=YEx" class="">Yes</td></tr><tr id="2352881f-86f1-809c-9ce5-e3a978c5fea7"><td id="miu?" class="">Adapter</td><td id="URa=" class="">Round-to-Rect</td><td id="iYdH" class="">At transition between shapes</td><td id="=YEx" class="">Yes</td></tr><tr id="2352881f-86f1-8035-8db2-d7746e3cab90"><td id="miu?" class="">Adapter</td><td id="URa=" class="">Rect-to-Round</td><td id="iYdH" class="">At transition between shapes</td><td id="=YEx" class="">Yes</td></tr></tbody></table><p id="2352881f-86f1-8011-a267-e749ea60e595" class=""><strong>(Future/Pro: Lateral, Saddle Tap, Wye, Spin-In, Volume Box, etc.)</strong></p><hr id="2352881f-86f1-8010-b70d-f892462e612d"/><h3 id="2352881f-86f1-80eb-a32f-e60a5ec69267" class=""><strong>C. Equipment</strong></h3><ul id="2352881f-86f1-80b6-bbef-ca0c9e4abc49" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Discrete device node</li></ul><ul id="2352881f-86f1-802e-a38a-fd85961c1574" class="bulleted-list"><li style="list-style-type:disc"><strong>Purpose:</strong> HVAC device at duct ends/midpoints (e.g., fans, air handlers, diffusers)</li></ul><ul id="2352881f-86f1-8067-9bb7-e2700f99eef3" class="bulleted-list"><li style="list-style-type:disc"><strong>Behavior:</strong> User-placed; connects to duct segments; has properties (CFM, static pressure, etc.)</li></ul><table id="2352881f-86f1-80e5-a65d-e1004b33534a" class="simple-table"><thead class="simple-table-header"><tr id="2352881f-86f1-80ef-b393-d318e747e8f8"><th id="MSli" class="simple-table-header-color simple-table-header">Equipment Type</th><th id="gfKp" class="simple-table-header-color simple-table-header">Placement</th><th id="lXZL" class="simple-table-header-color simple-table-header">Key Properties (MVP)</th></tr></thead><tbody><tr id="2352881f-86f1-8097-9eb7-ca39036d9620"><td id="MSli" class="">Supply Fan</td><td id="gfKp" class="">Duct start/end</td><td id="lXZL" class="">CFM, static pressure</td></tr><tr id="2352881f-86f1-80cf-8bbd-f23787397496"><td id="MSli" class="">Return Fan</td><td id="gfKp" class="">Duct start/end</td><td id="lXZL" class="">CFM, static pressure</td></tr><tr id="2352881f-86f1-8042-ba0f-c39268c7b95f"><td id="MSli" class="">Air Handling Unit</td><td id="gfKp" class="">In-line, branch</td><td id="lXZL" class="">CFM, ESP, filter type</td></tr><tr id="2352881f-86f1-8078-bb07-f68b23730272"><td id="MSli" class="">Diffuser</td><td id="gfKp" class="">Duct terminal</td><td id="lXZL" class="">CFM, noise criteria</td></tr><tr id="2352881f-86f1-80a0-8425-d3ea0d5d2413"><td id="MSli" class="">VAV Box</td><td id="gfKp" class="">In-line/terminal</td><td id="lXZL" class="">CFM, setpoint</td></tr><tr id="2352881f-86f1-80b0-a486-d83c68d6f38b"><td id="MSli" class="">Exhaust Fan</td><td id="gfKp" class="">Duct end</td><td id="lXZL" class="">CFM, static pressure</td></tr></tbody></table><p id="2352881f-86f1-803e-a9f4-cc7c27f1340d" class=""><em>(Future: Reheat Coil, HEPA Filter, Fire/Smoke Damper, etc.)</em></p><hr id="2352881f-86f1-805c-bedb-ced284859f25"/><h3 id="2352881f-86f1-803b-ab78-d02628dab2c6" class=""><strong>D. Room</strong></h3><ul id="2352881f-86f1-80ab-91f4-c319e26d8e0a" class="bulleted-list"><li style="list-style-type:disc"><strong>Type:</strong> Polygon or box region (represented as “room” node or polygon)</li></ul><ul id="2352881f-86f1-806a-b3e5-e03b50ebab6e" class="bulleted-list"><li style="list-style-type:disc"><strong>Purpose:</strong> Represents zone or space with airflow requirement</li></ul><ul id="2352881f-86f1-8009-aa2b-cc314cc59fa0" class="bulleted-list"><li style="list-style-type:disc"><strong>Behavior:</strong> Snap to duct terminal OR as named polygon; displays required/actual CFM.</li></ul><table id="2352881f-86f1-8045-9c4c-f0e334cddf92" class="simple-table"><thead class="simple-table-header"><tr id="2352881f-86f1-8079-be9e-f7157913bc8e"><th id=":CIk" class="simple-table-header-color simple-table-header">Room Property</th><th id="&gt;Gmm" class="simple-table-header-color simple-table-header">Type</th><th id="aH=s" class="simple-table-header-color simple-table-header">Usage</th></tr></thead><tbody><tr id="2352881f-86f1-8026-91c5-c6ea1fe33124"><td id=":CIk" class="">Room Name</td><td id="&gt;Gmm" class="">Text</td><td id="aH=s" class="">User- or auto-assigned</td></tr><tr id="2352881f-86f1-80dd-b481-d6ecb9709285"><td id=":CIk" class="">Room Area</td><td id="&gt;Gmm" class="">Number</td><td id="aH=s" class="">For future load calcs</td></tr><tr id="2352881f-86f1-800b-9db7-eed6b6aec811"><td id=":CIk" class="">Required CFM</td><td id="&gt;Gmm" class="">Number</td><td id="aH=s" class="">Based on code/ASHRAE table</td></tr><tr id="2352881f-86f1-8071-b799-ecae38a3166f"><td id=":CIk" class="">Supplied CFM</td><td id="&gt;Gmm" class="">Number</td><td id="aH=s" class="">Calculated, always shown</td></tr></tbody></table><hr id="2352881f-86f1-80a2-8313-d72c1365f0a5"/><h2 id="2352881f-86f1-802b-a740-e773b52bc2fb" class=""><strong>2. Drawing &amp; Behavior of Lines (Centerlines)</strong></h2><h3 id="2352881f-86f1-805f-b6ca-eae1a340acf2" class=""><strong>A. Creation</strong></h3><ul id="2352881f-86f1-800f-a672-ed3dc6551609" class="bulleted-list"><li style="list-style-type:disc"><strong>Trigger:</strong> Pencil Tool ON (FAB)</li></ul><ul id="2352881f-86f1-8061-9b88-dee354a26259" class="bulleted-list"><li style="list-style-type:disc"><strong>Interaction:</strong><ul id="2352881f-86f1-8012-8686-e57b36952d23" class="bulleted-list"><li style="list-style-type:circle"><strong>Left-click:</strong> Place node (start or continue line)</li></ul><ul id="2352881f-86f1-80db-8023-f7738c8bf1aa" class="bulleted-list"><li style="list-style-type:circle"><strong>Double-click:</strong> End current line/segment</li></ul><ul id="2352881f-86f1-80ba-90d1-ff70220976ce" class="bulleted-list"><li style="list-style-type:circle"><strong>Snap:</strong> Lines snap to grid, existing nodes, or equipment/room</li></ul><ul id="2352881f-86f1-8003-8c70-f29919c76347" class="bulleted-list"><li style="list-style-type:circle"><strong>Shift key:</strong> Constrains angle to 0/45/90° for orthogonal/diagonal lines</li></ul><ul id="2352881f-86f1-803a-a4c5-c07cbddcd31c" class="bulleted-list"><li style="list-style-type:circle"><strong>Right-click:</strong> Pan, even while drawing</li></ul></li></ul><h3 id="2352881f-86f1-8048-a507-eee34a91a697" class=""><strong>B. Behavior and States</strong></h3><ul id="2352881f-86f1-80af-8f8b-d6149b419bd5" class="bulleted-list"><li style="list-style-type:disc"><strong>Visual State:</strong><ul id="2352881f-86f1-802e-868e-fc31b0d4fc9e" class="bulleted-list"><li style="list-style-type:circle"><strong>Active Drawing:</strong> Line is orange (#FF9800), semi-opaque as it’s being drawn</li></ul><ul id="2352881f-86f1-8071-bcdb-d244e665357f" class="bulleted-list"><li style="list-style-type:circle"><strong>Completed (Unselected):</strong> Neutral grey (#BDBDBD), 100% opacity</li></ul><ul id="2352881f-86f1-801b-a498-f4190186229f" class="bulleted-list"><li style="list-style-type:circle"><strong>Selected:</strong> Blue highlight (#1976D2) plus nodes/handles visible</li></ul><ul id="2352881f-86f1-80be-9c47-f58de215b624" class="bulleted-list"><li style="list-style-type:circle"><strong>Warning/Invalid:</strong> Red or yellow glow if velocity/pressure limit exceeded (matches warnings bar/panel)</li></ul></li></ul><ul id="2352881f-86f1-80d7-bd69-d8fd57dbccbc" class="bulleted-list"><li style="list-style-type:disc"><strong>Node Types:</strong><ul id="2352881f-86f1-8022-9216-ed02e517e7e2" class="bulleted-list"><li style="list-style-type:circle"><strong>Standard Node:</strong> White fill, blue border (when selected)</li></ul><ul id="2352881f-86f1-80e4-92a1-e786db2083fc" class="bulleted-list"><li style="list-style-type:circle"><strong>Fitting Node:</strong> Special shape/icon (circle for elbows, tee, etc.)</li></ul><ul id="2352881f-86f1-8077-a909-f931a261d005" class="bulleted-list"><li style="list-style-type:circle"><strong>Equipment Node:</strong> Device icon at node</li></ul></li></ul><ul id="2352881f-86f1-80a6-b65b-e7e11d49154b" class="bulleted-list"><li style="list-style-type:disc"><strong>Editing:</strong><ul id="2352881f-86f1-8031-aa03-edbc42b4ddd0" class="bulleted-list"><li style="list-style-type:circle"><strong>Select:</strong> Click on line or node (Pencil OFF)</li></ul><ul id="2352881f-86f1-808f-a1a7-dff640456ee8" class="bulleted-list"><li style="list-style-type:circle"><strong>Move:</strong> Drag node (drags connected segments)</li></ul><ul id="2352881f-86f1-808f-a0f7-c352b2aaba8a" class="bulleted-list"><li style="list-style-type:circle"><strong>Add Node:</strong> Click on line to add mid-node</li></ul><ul id="2352881f-86f1-804a-b729-c9cf6add005e" class="bulleted-list"><li style="list-style-type:circle"><strong>Delete Segment/Node:</strong> Select, then press <code>Del</code> or context action</li></ul><ul id="2352881f-86f1-8096-95e7-faad90f26814" class="bulleted-list"><li style="list-style-type:circle"><strong>Properties:</strong> Select → edit in Context Property Panel</li></ul></li></ul><ul id="2352881f-86f1-8077-94fb-d39f18e8d9bf" class="bulleted-list"><li style="list-style-type:disc"><strong>Snap &amp; Constraint:</strong><ul id="2352881f-86f1-8026-9514-f1835f871f2f" class="bulleted-list"><li style="list-style-type:circle"><strong>Snap-to-grid</strong>: If grid enabled, cursor snaps to grid intersections</li></ul><ul id="2352881f-86f1-8013-ac43-c8b4680a00b8" class="bulleted-list"><li style="list-style-type:circle"><strong>Smart Snap:</strong> Auto-aligns to other nearby lines or endpoints</li></ul><ul id="2352881f-86f1-8000-9d2f-c3a0276f16e6" class="bulleted-list"><li style="list-style-type:circle"><strong>Connection Rules:</strong> No “floating” lines—all lines must start/end at room, equipment, or another duct node (warn if not connected)</li></ul></li></ul><h3 id="2352881f-86f1-80bd-b0e8-ed4e5d108c1c" class=""><strong>C. Converting to 3D</strong></h3><ul id="2352881f-86f1-802b-a9be-e68ceb3c1307" class="bulleted-list"><li style="list-style-type:disc"><strong>Action:</strong> “Build Ductwork” button in Results/Warnings Bar or Calculation Bar</li></ul><ul id="2352881f-86f1-8070-90f6-fd4c168d9c56" class="bulleted-list"><li style="list-style-type:disc"><strong>Effect:</strong> All centerlines are extruded into 3D duct/fitting geometry using default/user-set properties</li></ul><ul id="2352881f-86f1-808e-aa74-d006d93512a2" class="bulleted-list"><li style="list-style-type:disc"><strong>Fittings:</strong> Auto-inserted at each relevant node</li></ul><ul id="2352881f-86f1-8009-bac2-e39e0ea3e151" class="bulleted-list"><li style="list-style-type:disc"><strong>Validation:</strong> Warns if open/unconnected lines, overlaps, code violations</li></ul><hr id="2352881f-86f1-80bd-9a78-c187e6232556"/><h2 id="2352881f-86f1-80cc-a315-c4171db70bff" class=""><strong>3. Element Metadata &amp; Properties</strong></h2><ul id="2352881f-86f1-80a8-8939-f406693bce7a" class="bulleted-list"><li style="list-style-type:disc"><strong>Duct:</strong><ul id="2352881f-86f1-80d0-b5aa-fe63523aa57f" class="bulleted-list"><li style="list-style-type:circle">Name/Tag (auto/incremental, e.g., D-101)</li></ul><ul id="2352881f-86f1-80d5-83d4-ca79667737ff" class="bulleted-list"><li style="list-style-type:circle">Size (W x H or Dia)</li></ul><ul id="2352881f-86f1-8082-a75e-c452b1a66597" class="bulleted-list"><li style="list-style-type:circle">Material (steel, aluminum, etc.)</li></ul><ul id="2352881f-86f1-801d-a3b0-cff6779e588d" class="bulleted-list"><li style="list-style-type:circle">Insulation (Y/N, thickness)</li></ul><ul id="2352881f-86f1-8054-85f8-e22b0d875f66" class="bulleted-list"><li style="list-style-type:circle">Static pressure loss (auto-calc)</li></ul><ul id="2352881f-86f1-803e-b685-ead0b5fdf5b6" class="bulleted-list"><li style="list-style-type:circle">Flow rate (auto-calc or assigned)</li></ul></li></ul><ul id="2352881f-86f1-8019-b2d7-ea92a91dd906" class="bulleted-list"><li style="list-style-type:disc"><strong>Fitting:</strong><ul id="2352881f-86f1-80e0-a92a-e10b0d574069" class="bulleted-list"><li style="list-style-type:circle">Type/subtype</li></ul><ul id="2352881f-86f1-8082-93c5-e103c0238239" class="bulleted-list"><li style="list-style-type:circle">Connected ducts (names)</li></ul><ul id="2352881f-86f1-80e9-9671-f8ac3a7175de" class="bulleted-list"><li style="list-style-type:circle">Loss coefficient (K-factor, auto from table)</li></ul></li></ul><ul id="2352881f-86f1-8036-9657-c4fd27ee4aec" class="bulleted-list"><li style="list-style-type:disc"><strong>Equipment:</strong><ul id="2352881f-86f1-80ff-8217-fb88f67d2b88" class="bulleted-list"><li style="list-style-type:circle">Name/Tag</li></ul><ul id="2352881f-86f1-800f-be06-d2638c8f9573" class="bulleted-list"><li style="list-style-type:circle">Device type</li></ul><ul id="2352881f-86f1-8008-9068-c69c6fc3fe3b" class="bulleted-list"><li style="list-style-type:circle">Design/required CFM, actual CFM</li></ul><ul id="2352881f-86f1-8031-bf9a-ff314428ab63" class="bulleted-list"><li style="list-style-type:circle">Status/notes</li></ul></li></ul><ul id="2352881f-86f1-8032-801c-f175135e7420" class="bulleted-list"><li style="list-style-type:disc"><strong>Room:</strong><ul id="2352881f-86f1-80de-bffd-f92f90044c45" class="bulleted-list"><li style="list-style-type:circle">Name</li></ul><ul id="2352881f-86f1-80f9-8e0b-f7feddb3212d" class="bulleted-list"><li style="list-style-type:circle">Area</li></ul><ul id="2352881f-86f1-80a8-8dda-dde169b04dd6" class="bulleted-list"><li style="list-style-type:circle">Required CFM</li></ul><ul id="2352881f-86f1-8096-8b23-fa2c83fde149" class="bulleted-list"><li style="list-style-type:circle">Actual supplied CFM</li></ul><ul id="2352881f-86f1-80a6-84b4-d3b28860d4a1" class="bulleted-list"><li style="list-style-type:circle">Code reference (for compliance)</li></ul></li></ul><hr id="2352881f-86f1-8053-a6be-cd1fe7d9b213"/><h2 id="2352881f-86f1-8095-8817-e4c618e08b9e" class=""><strong>4. Accessibility &amp; Usability</strong></h2><ul id="2352881f-86f1-8080-a23a-db8ac954385c" class="bulleted-list"><li style="list-style-type:disc"><strong>All drawing/editing features are keyboard-accessible:</strong><ul id="2352881f-86f1-80e2-8b46-cc0f8ff835e2" class="bulleted-list"><li style="list-style-type:circle">Tab to cycle elements</li></ul><ul id="2352881f-86f1-80af-85df-f2825ab3833f" class="bulleted-list"><li style="list-style-type:circle">Arrow keys to move selected node/segment</li></ul><ul id="2352881f-86f1-807c-98a1-deebf68c45e2" class="bulleted-list"><li style="list-style-type:circle">Enter to edit properties</li></ul></li></ul><ul id="2352881f-86f1-8088-99fa-f9f2d9a86f3b" class="bulleted-list"><li style="list-style-type:disc"><strong>Visual states have high contrast</strong></li></ul><ul id="2352881f-86f1-80fc-8c99-c815414de9d0" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltips and ARIA labels</strong> for all controls and drawn elements</li></ul><hr id="2352881f-86f1-803b-a525-d0f0c0bfd50d"/><h2 id="2352881f-86f1-80cd-a227-f4e00aa6a838" class=""><strong>5. Professional Rationale</strong></h2><ul id="2352881f-86f1-800a-a6f5-ff244a0348a1" class="bulleted-list"><li style="list-style-type:disc"><strong>Mimics professional CAD/BIM drawing but optimized for duct systems</strong></li></ul><ul id="2352881f-86f1-806c-a63f-ffe2ef9d6eb4" class="bulleted-list"><li style="list-style-type:disc"><strong>Granular element filter</strong> (see prior doc) ensures efficient workflows and error prevention</li></ul><ul id="2352881f-86f1-80c1-8ff8-e6dd31af8458" class="bulleted-list"><li style="list-style-type:disc"><strong>Auto-insertion and smart snapping</strong> support both rapid design and code compliance</li></ul><hr id="2352881f-86f1-80b6-999a-ea2476b5c4e9"/><h2 id="2352881f-86f1-80bf-99cd-dbe225716169" class=""><strong>6. Example Visual Guide</strong></h2><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2352881f-86f1-8046-b2a3-db6700b11d2c" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">[Room A]—(Duct)—[Elbow]—(Duct)—[Tee]—(Duct)—[Fan]
           │
        (Branch)
         [Room B]
</code></pre><ul id="2352881f-86f1-80f7-85e6-de5fb92d31c9" class="bulleted-list"><li style="list-style-type:disc">Blue: selected, Orange: drawing, Red/yellow: warning, Grey: normal</li></ul><hr id="2352881f-86f1-8049-a25a-f5c9ee6220e4"/><p id="2352881f-86f1-80ea-af66-fecd71df93e4" class=""><strong>This documentation is the canonical specification for all Drawing Elements, fitting types, and centerline behaviors in the Air Duct Sizer 3D Tool MVP. All drawing behaviors, editing options, and fitting/equipment metadata are covered for engineering-grade implementation and team handoff.</strong></p><p id="2352881f-86f1-80d4-b3e9-f6b1a2f3a191" class="">Let me know if you want visuals or a rendered element sample!</p></details></li></ul><ul id="2352881f-86f1-8028-a6fc-ee1ff09539ee" class="toggle"><li><details open=""><summary><strong>Drawing Elements Filter Panel</strong></summary><h1 id="2352881f-86f1-8067-804b-d5912965f7f0" class=""><strong>Drawing Elements Filter Panel — Hierarchical Specification</strong></h1><hr id="2352881f-86f1-80c3-b252-fb8be213b9e2"/><h2 id="2352881f-86f1-80e0-9a68-e5ff18e44b9f" class=""><strong>Purpose</strong></h2><p id="2352881f-86f1-804e-96ea-dc82aebb1324" class="">The Drawing Elements Filter Panel enables users to <strong>filter and target specific element types</strong> for selection and editing in the 3D workspace, supporting both high-level categories and granular item selection.</p><p id="2352881f-86f1-8020-a126-caa81b1aa98f" class="">This prevents accidental modifications, accelerates batch actions, and mirrors industry-standard engineering workflows.</p><hr id="2352881f-86f1-802d-892d-f1537a69be68"/><h2 id="2352881f-86f1-8031-af03-dce5815c8b9b" class=""><strong>Naming</strong></h2><ul id="2352881f-86f1-80c0-af64-ddde11fd8858" class="bulleted-list"><li style="list-style-type:disc"><strong>Canonical Name:</strong> Drawing Elements Filter Panel</li></ul><ul id="2352881f-86f1-8072-812d-d13b5339979a" class="bulleted-list"><li style="list-style-type:disc"><strong>UI Label (ARIA):</strong> “Drawing Elements Filter”</li></ul><ul id="2352881f-86f1-802d-8ad2-e123a2362cdb" class="bulleted-list"><li style="list-style-type:disc"><strong>Retracted Icon:</strong> Filter/Funnel (Material “filter_list”)</li></ul><ul id="2352881f-86f1-8075-a56a-fbc2ef766550" class="bulleted-list"><li style="list-style-type:disc"><strong>Expanded Header:</strong> “Drawing Elements” + icon</li></ul><hr id="2352881f-86f1-8013-9b21-c4bb29f47ccf"/><h2 id="2352881f-86f1-8082-a60f-e31fdc11a3bf" class=""><strong>Placement</strong></h2><ul id="2352881f-86f1-8016-a1c1-dd4253d8e3f5" class="bulleted-list"><li style="list-style-type:disc"><strong>Vertical Anchor:</strong> Directly below the Drawing Tool FAB, right edge of canvas.</li></ul><ul id="2352881f-86f1-80cb-8a03-c1eb4e0d1f89" class="bulleted-list"><li style="list-style-type:disc"><strong>Spacing:</strong> 8–16px below FAB, always visible when collapsed (icon only).</li></ul><hr id="2352881f-86f1-807a-a8fb-d0221361c802"/><h2 id="2352881f-86f1-803a-a3ca-f274aabd18dc" class=""><strong>States &amp; Interactivity</strong></h2><h3 id="2352881f-86f1-804c-a586-d03c8c444c3a" class=""><strong>A. Retracted State</strong></h3><ul id="2352881f-86f1-8014-a5bb-f2ead21d64a3" class="bulleted-list"><li style="list-style-type:disc"><strong>Icon Only:</strong> Circular filter icon (~44x44px), frosted glass, subtle shadow.</li></ul><ul id="2352881f-86f1-8063-b3cf-ca6967a1e454" class="bulleted-list"><li style="list-style-type:disc"><strong>Tooltip:</strong> “Filter selectable element types”</li></ul><ul id="2352881f-86f1-80ac-a1b6-cce9d1e0534e" class="bulleted-list"><li style="list-style-type:disc"><strong>Keyboard/Pointer activation:</strong> Expands panel.</li></ul><h3 id="2352881f-86f1-80c9-9d60-c440c20e7a79" class=""><strong>B. Expanded State</strong></h3><ul id="2352881f-86f1-8023-975a-f6349ae77b36" class="bulleted-list"><li style="list-style-type:disc"><strong>Panel appears leftward/downward</strong> from icon anchor (180ms animation).</li></ul><ul id="2352881f-86f1-8023-a32b-e281f0c32b42" class="bulleted-list"><li style="list-style-type:disc"><strong>Header:</strong> “Drawing Elements” (bold, 16px) + filter icon.</li></ul><ul id="2352881f-86f1-8015-a036-cbb7cc8dbf78" class="bulleted-list"><li style="list-style-type:disc"><strong>Main Category Checkboxes:</strong> (all checked by default)<ul id="2352881f-86f1-8075-a866-e66e62f52d87" class="bulleted-list"><li style="list-style-type:circle">[✔] <strong>Duct</strong></li></ul><ul id="2352881f-86f1-800d-9c5c-f24f1df48351" class="bulleted-list"><li style="list-style-type:circle">[✔] <strong>Fitting</strong></li></ul><ul id="2352881f-86f1-8043-a437-f660d4ebaa80" class="bulleted-list"><li style="list-style-type:circle">[✔] <strong>Equipment</strong></li></ul><ul id="2352881f-86f1-8034-a3ea-dd305ca50cf0" class="bulleted-list"><li style="list-style-type:circle">[✔] <strong>Room</strong></li></ul></li></ul><ul id="2352881f-86f1-80d7-ad71-c4c7c7780686" class="bulleted-list"><li style="list-style-type:disc"><strong>Expand/Collapse Arrows:</strong> Each category can be expanded (▸/▾) to show a detailed list:<ul id="2352881f-86f1-805a-8cf6-e6f7119fbad3" class="bulleted-list"><li style="list-style-type:circle"><strong>Duct:</strong> Sub-types (e.g., Main Duct, Branch Duct, Return Duct, etc.)</li></ul><ul id="2352881f-86f1-8041-a618-c7d47fb51f4f" class="bulleted-list"><li style="list-style-type:circle"><strong>Fitting:</strong> (e.g., Elbow, Tee, Reducer, Damper…)</li></ul><ul id="2352881f-86f1-800f-88db-ee315d164d7f" class="bulleted-list"><li style="list-style-type:circle"><strong>Equipment:</strong> (e.g., Fan, AHU, Diffuser…)</li></ul><ul id="2352881f-86f1-80dd-ad14-e1752618cd0d" class="bulleted-list"><li style="list-style-type:circle"><strong>Room:</strong> (e.g., Room 101, Lab A, Office 2.1… auto-populated from project data)</li></ul></li></ul><ul id="2352881f-86f1-80b6-87e4-f28aeac814c1" class="bulleted-list"><li style="list-style-type:disc"><strong>Sub-list checkboxes:</strong> Each item can be individually checked/unchecked.</li></ul><ul id="2352881f-86f1-8048-8dcf-f7a6911b2f8f" class="bulleted-list"><li style="list-style-type:disc"><strong>Reset All:</strong> Link at the bottom to re-check all.</li></ul><ul id="2352881f-86f1-805c-a175-f8213718193c" class="bulleted-list"><li style="list-style-type:disc"><strong>State Persistence:</strong> Remembers per project/session.</li></ul><ul id="2352881f-86f1-8063-9505-d2302a0760d1" class="bulleted-list"><li style="list-style-type:disc"><strong>Collapse triggers:</strong> Click-away, Esc key, keyboard navigation.</li></ul><hr id="2352881f-86f1-8090-bcf1-e805d87a1925"/><h2 id="2352881f-86f1-80a4-8977-d19e30a22c5c" class=""><strong>Accessibility</strong></h2><ul id="2352881f-86f1-805a-ba2f-fcad32dae201" class="bulleted-list"><li style="list-style-type:disc"><strong>ARIA structure:</strong><ul id="2352881f-86f1-8036-a3e5-c72a88c3d90b" class="bulleted-list"><li style="list-style-type:circle">Panel: <code>role=&quot;region&quot;</code> labeled “Drawing Elements Filter”</li></ul><ul id="2352881f-86f1-8032-85ec-c3935ae692b1" class="bulleted-list"><li style="list-style-type:circle">Main checkboxes: <code>role=&quot;checkbox&quot;</code>, labeled with category and state</li></ul><ul id="2352881f-86f1-80ca-be8c-ed85759ed3de" class="bulleted-list"><li style="list-style-type:circle">Expand/collapse: <code>aria-expanded</code>, full keyboard support</li></ul><ul id="2352881f-86f1-8070-be69-f2e71a97614b" class="bulleted-list"><li style="list-style-type:circle">Each sub-item: labeled with name/type</li></ul></li></ul><ul id="2352881f-86f1-80ce-9fb1-c5ea6eda6f76" class="bulleted-list"><li style="list-style-type:disc"><strong>Tab, arrows:</strong> For moving between controls; Space/Enter to toggle.</li></ul><ul id="2352881f-86f1-80de-9def-ddf10dca8e85" class="bulleted-list"><li style="list-style-type:disc"><strong>Panel close:</strong> Esc key returns focus to filter icon.</li></ul><hr id="2352881f-86f1-80de-9ee2-e71fdd3c31da"/><h2 id="2352881f-86f1-807d-aab3-cbbc33bf6370" class=""><strong>Visual &amp; Style Guide</strong></h2><table id="2352881f-86f1-8069-96a6-d09c11b66600" class="simple-table"><thead class="simple-table-header"><tr id="2352881f-86f1-80c6-a6bb-e0f9cdb8cdbc"><th id="Pgcw" class="simple-table-header-color simple-table-header">Element</th><th id="p=oA" class="simple-table-header-color simple-table-header">Style / Color</th></tr></thead><tbody><tr id="2352881f-86f1-80e5-a225-e60cc92bb4a8"><td id="Pgcw" class="">Panel Background</td><td id="p=oA" class="">Frosted white (#FFFFFFEE), rounded 16px corners</td></tr><tr id="2352881f-86f1-80d2-93c6-c60138829e39"><td id="Pgcw" class="">Border</td><td id="p=oA" class="">Liquid glass #E0E0E0, 18% opacity, 1px</td></tr><tr id="2352881f-86f1-80a3-83e9-f9cbacc45232"><td id="Pgcw" class="">Header Text/Icon</td><td id="p=oA" class="">Charcoal #222222, filter icon #B0BEC5</td></tr><tr id="2352881f-86f1-80ad-ae8b-e5014029f11b"><td id="Pgcw" class="">Checkbox (on)</td><td id="p=oA" class="">Blue #1565C0, white check, blue border</td></tr><tr id="2352881f-86f1-801b-a2e1-c654e3bf15ba"><td id="Pgcw" class="">Checkbox (off)</td><td id="p=oA" class="">Grey #B0BEC5 outline</td></tr><tr id="2352881f-86f1-8078-8997-ecc767ce8564"><td id="Pgcw" class="">Expand/Collapse</td><td id="p=oA" class="">Arrow #1976D2, rotates 90° when expanded</td></tr><tr id="2352881f-86f1-8074-97fe-f4b4b079cfe1"><td id="Pgcw" class="">Sub-item</td><td id="p=oA" class="">Indented, lighter text (#424242), hover blue</td></tr><tr id="2352881f-86f1-805c-a413-d942e8eb4b8e"><td id="Pgcw" class="">Reset Link</td><td id="p=oA" class="">Blue #1976D2, underlined on hover</td></tr></tbody></table><hr id="2352881f-86f1-8065-b236-fc36f00a7c95"/><h2 id="2352881f-86f1-80bc-9058-f04c83468cb7" class=""><strong>Responsiveness</strong></h2><ul id="2352881f-86f1-8099-8226-dd16cb4ddde4" class="bulleted-list"><li style="list-style-type:disc"><strong>Panel always stays within viewport.</strong></li></ul><ul id="2352881f-86f1-8038-a49d-f8611d5e0aac" class="bulleted-list"><li style="list-style-type:disc"><strong>Expands vertically up to 320px, scrollable if too many sub-items.</strong></li></ul><ul id="2352881f-86f1-8007-868a-ecd45c44382a" class="bulleted-list"><li style="list-style-type:disc"><strong>Remembers last expanded/collapsed state per session.</strong></li></ul><hr id="2352881f-86f1-80a7-8ca9-d721105418e2"/><h2 id="2352881f-86f1-8080-ac2b-daafbdd92704" class=""><strong>Behavior Example</strong></h2><ol type="1" id="2352881f-86f1-8028-a22b-d240f935d86c" class="numbered-list" start="1"><li><strong>Panel retracted:</strong> Just a filter icon below FAB.</li></ol><ol type="1" id="2352881f-86f1-8044-bfe8-cf8d5666c39c" class="numbered-list" start="2"><li><strong>User clicks icon:</strong> Panel expands, showing all four main checkboxes.</li></ol><ol type="1" id="2352881f-86f1-80ef-8b60-eea980c21064" class="numbered-list" start="3"><li><strong>User clicks arrow beside “Room”:</strong> Expands to show all room names, each with a checkbox.</li></ol><ol type="1" id="2352881f-86f1-80fd-beb4-ed2114bb7c43" class="numbered-list" start="4"><li><strong>User unchecks “Lab A”:</strong> This room becomes unselectable in 3D canvas.</li></ol><ol type="1" id="2352881f-86f1-8045-8cd9-fc3712374266" class="numbered-list" start="5"><li><strong>User clicks Reset:</strong> All main and sub-items are checked again.</li></ol><ol type="1" id="2352881f-86f1-8055-b00b-ea893e9e559c" class="numbered-list" start="6"><li><strong>Panel collapses:</strong> State is saved.</li></ol><hr id="2352881f-86f1-800f-94e3-ce460b3ccd93"/><h2 id="2352881f-86f1-803a-b58d-cf1a03d1c4c7" class=""><strong>UX Rationale</strong></h2><ul id="2352881f-86f1-8032-8af2-f7fce54b8dee" class="bulleted-list"><li style="list-style-type:disc"><strong>Granular control</strong>: Allows selection/filtering at both type and instance level.</li></ul><ul id="2352881f-86f1-80c3-abd2-fe718050f74a" class="bulleted-list"><li style="list-style-type:disc"><strong>Error prevention</strong>: Prevents accidental edits to the wrong system or room.</li></ul><ul id="2352881f-86f1-807a-81de-ef70b9fdc8f2" class="bulleted-list"><li style="list-style-type:disc"><strong>Professional alignment</strong>: Mimics layer/visibility/selection filters in CAD/BIM.</li></ul><hr id="2352881f-86f1-8092-827f-c777dbea5666"/><h2 id="2352881f-86f1-804f-a4bc-d9855890113c" class=""><strong>Authoritative Summary</strong></h2><blockquote id="2352881f-86f1-8073-96b5-da333ef2e2a5" class="">The Drawing Elements Filter Panel is a right-anchored, retractable utility below the Drawing Tool FAB.<p id="2352881f-86f1-80e1-b776-f3df606a2ad5" class="">It allows users to filter and select by main element type (Duct, Fitting, Equipment, Room) and, via expandable lists, by individual item.</p><p id="2352881f-86f1-805e-9cd1-ee915a1574df" class="">All states, labels, and focus behaviors are fully accessible and persist per session.</p><p id="2352881f-86f1-80d4-a050-d6ebd147affc" class="">The design follows best-in-class engineering UI patterns for both speed and precision.</p></blockquote><hr id="2352881f-86f1-80a6-b604-e4055598fdf0"/></details></li></ul><ul id="2312881f-86f1-80e4-97a2-e7c5f6930c7b" class="toggle"><li><details open=""><summary><strong>PDF Import Feature</strong></summary><ul id="2312881f-86f1-80d8-93f9-f154121bebb5" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Tool must enable pdf import for front view and plan view</span><div class="indented"></div></li></ul><ul id="2312881f-86f1-800e-9e6a-ff3d4e6040e8" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Tool must have a PDF import layout wizard capable of pop-up</span><div class="indented"><ul id="2312881f-86f1-8060-892d-d9e03081fbef" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Wizard must have scaling input field - Canvas scale must follow the user’s input and reflect to the scaling field in the tool screen.</span><div class="indented"></div></li></ul><ul id="2312881f-86f1-8044-9245-c68a655d859c" class="toggle"><li><details open=""><summary>Codes for pop-up hover screen</summary><ul id="2312881f-86f1-806e-af84-dfd6a797bf30" class="toggle"><li><details open=""><summary>morphing-popover</summary><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-80e4-886b-e7e3abed5be7" class="code"><code class="language-TypeScript">&#x27;use client&#x27;;

import {
  useState,
  useId,
  useRef,
  useEffect,
  createContext,
  useContext,
  isValidElement,
} from &#x27;react&#x27;;
import {
  AnimatePresence,
  MotionConfig,
  motion,
  Transition,
  Variants,
} from &#x27;motion/react&#x27;;
import { useClickOutside } from &#x27;@/hooks/use-click-outside&#x27;;
import { cn } from &#x27;@/lib/utils&#x27;;

const TRANSITION = {
  type: &#x27;spring&#x27;,
  bounce: 0.1,
  duration: 0.4,
};

type MorphingPopoverContextValue = {
  isOpen: boolean;
  open: () =&gt; void;
  close: () =&gt; void;
  uniqueId: string;
  variants?: Variants;
};

const MorphingPopoverContext =
  createContext&lt;MorphingPopoverContextValue | null&gt;(null);

function usePopoverLogic({
  defaultOpen = false,
  open: controlledOpen,
  onOpenChange,
}: {
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) =&gt; void;
} = {}) {
  const uniqueId = useId();
  const [uncontrolledOpen, setUncontrolledOpen] = useState(defaultOpen);

  const isOpen = controlledOpen ?? uncontrolledOpen;

  const open = () =&gt; {
    if (controlledOpen === undefined) {
      setUncontrolledOpen(true);
    }
    onOpenChange?.(true);
  };

  const close = () =&gt; {
    if (controlledOpen === undefined) {
      setUncontrolledOpen(false);
    }
    onOpenChange?.(false);
  };

  return { isOpen, open, close, uniqueId };
}

export type MorphingPopoverProps = {
  children: React.ReactNode;
  transition?: Transition;
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) =&gt; void;
  variants?: Variants;
  className?: string;
} &amp; React.ComponentProps&lt;&#x27;div&#x27;&gt;;

function MorphingPopover({
  children,
  transition = TRANSITION,
  defaultOpen,
  open,
  onOpenChange,
  variants,
  className,
  ...props
}: MorphingPopoverProps) {
  const popoverLogic = usePopoverLogic({ defaultOpen, open, onOpenChange });

  return (
    &lt;MorphingPopoverContext.Provider value={{ ...popoverLogic, variants }}&gt;
      &lt;MotionConfig transition={transition}&gt;
        &lt;div
          className={cn(&#x27;relative flex items-center justify-center&#x27;, className)}
          key={popoverLogic.uniqueId}
          {...props}
        &gt;
          {children}
        &lt;/div&gt;
      &lt;/MotionConfig&gt;
    &lt;/MorphingPopoverContext.Provider&gt;
  );
}

export type MorphingPopoverTriggerProps = {
  asChild?: boolean;
  children: React.ReactNode;
  className?: string;
} &amp; React.ComponentProps&lt;typeof motion.button&gt;;

function MorphingPopoverTrigger({
  children,
  className,
  asChild = false,
  ...props
}: MorphingPopoverTriggerProps) {
  const context = useContext(MorphingPopoverContext);
  if (!context) {
    throw new Error(
      &#x27;MorphingPopoverTrigger must be used within MorphingPopover&#x27;
    );
  }

  if (asChild &amp;&amp; isValidElement(children)) {
    const MotionComponent = motion.create(
      children.type as React.ForwardRefExoticComponent&lt;any&gt;
    );
    const childProps = children.props as Record&lt;string, unknown&gt;;

    return (
      &lt;MotionComponent
        {...childProps}
        onClick={context.open}
        layoutId={`popover-trigger-${context.uniqueId}`}
        className={childProps.className}
        key={context.uniqueId}
        aria-expanded={context.isOpen}
        aria-controls={`popover-content-${context.uniqueId}`}
      /&gt;
    );
  }

  return (
    &lt;motion.div
      key={context.uniqueId}
      layoutId={`popover-trigger-${context.uniqueId}`}
      onClick={context.open}
    &gt;
      &lt;motion.button
        {...props}
        layoutId={`popover-label-${context.uniqueId}`}
        key={context.uniqueId}
        className={className}
        aria-expanded={context.isOpen}
        aria-controls={`popover-content-${context.uniqueId}`}
      &gt;
        {children}
      &lt;/motion.button&gt;
    &lt;/motion.div&gt;
  );
}

export type MorphingPopoverContentProps = {
  children: React.ReactNode;
  className?: string;
} &amp; React.ComponentProps&lt;typeof motion.div&gt;;

function MorphingPopoverContent({
  children,
  className,
  ...props
}: MorphingPopoverContentProps) {
  const context = useContext(MorphingPopoverContext);
  if (!context)
    throw new Error(
      &#x27;MorphingPopoverContent must be used within MorphingPopover&#x27;
    );

  const ref = useRef&lt;HTMLDivElement&gt;(null);
  useClickOutside(ref, context.close);

  useEffect(() =&gt; {
    if (!context.isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) =&gt; {
      if (event.key === &#x27;Escape&#x27;) context.close();
    };

    document.addEventListener(&#x27;keydown&#x27;, handleKeyDown);
    return () =&gt; document.removeEventListener(&#x27;keydown&#x27;, handleKeyDown);
  }, [context.isOpen, context.close]);

  return (
    &lt;AnimatePresence&gt;
      {context.isOpen &amp;&amp; (
        &lt;&gt;
          &lt;motion.div
            {...props}
            ref={ref}
            layoutId={`popover-trigger-${context.uniqueId}`}
            key={context.uniqueId}
            id={`popover-content-${context.uniqueId}`}
            role=&#x27;dialog&#x27;
            aria-modal=&#x27;true&#x27;
            className={cn(
              &#x27;absolute overflow-hidden rounded-md border border-zinc-950/10 bg-white p-2 text-zinc-950 shadow-md dark:border-zinc-50/10 dark:bg-zinc-700 dark:text-zinc-50&#x27;,
              className
            )}
            initial=&#x27;initial&#x27;
            animate=&#x27;animate&#x27;
            exit=&#x27;exit&#x27;
            variants={context.variants}
          &gt;
            {children}
          &lt;/motion.div&gt;
        &lt;/&gt;
      )}
    &lt;/AnimatePresence&gt;
  );
}

export { MorphingPopover, MorphingPopoverTrigger, MorphingPopoverContent };
</code></pre></details></li></ul><ul id="2312881f-86f1-8041-9b68-ca807a998192" class="toggle"><li><details open=""><summary>use-click-outside</summary><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="2312881f-86f1-80fb-b7ba-ec9f3f51a98e" class="code"><code class="language-TypeScript">import { RefObject, useEffect } from &#x27;react&#x27;

type Handler = (event: MouseEvent | TouchEvent) =&gt; void

export function useClickOutside&lt;T extends HTMLElement = HTMLElement&gt;(
  ref: RefObject&lt;T&gt;,
  handler: Handler,
  mouseEvent: &#x27;mousedown&#x27; | &#x27;mouseup&#x27; = &#x27;mousedown&#x27;
): void {
  useEffect(() =&gt; {
    const listener = (event: MouseEvent | TouchEvent) =&gt; {
      const el = ref?.current
      const target = event.target

      // Do nothing if clicking ref&#x27;s element or descendent elements
      if (!el || !target || el.contains(target as Node)) {
        return
      }

      handler(event)
    }

    document.addEventListener(mouseEvent, listener)
    document.addEventListener(&#x27;touchstart&#x27;, listener)

    return () =&gt; {
      document.removeEventListener(mouseEvent, listener)
      document.removeEventListener(&#x27;touchstart&#x27;, listener)
    }
  }, [ref, handler, mouseEvent])
}</code></pre></details></li></ul></details></li></ul></div></li></ul><ul id="2312881f-86f1-800a-9d36-c6566cb5b9cd" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">PDF import layout wizard to be placed at the bottom of the screen</span><div class="indented"></div></li></ul></details></li></ul><ul id="2312881f-86f1-8010-b07e-ead43b36c759" class="toggle"><li><details open=""><summary><strong>Canvas Feature</strong></summary><ul id="2312881f-86f1-80b4-9978-c8118426007c" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Air Duct Sizer tool must have a 3D Canvas</span><div class="indented"></div></li></ul></details></li></ul></div></details><details open=""><summary style="font-weight:600;font-size:1.25em;line-height:1.3;margin:0">Handling Bug Reports from User</summary><div class="indented"><ul id="2312881f-86f1-80e4-8244-c872784e809c" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">Report Bug</span><div class="indented"></div></li></ul></div></details><details open=""><summary style="font-weight:600;font-size:1.25em;line-height:1.3;margin:0">Security</summary><div class="indented"><p id="2342881f-86f1-80b4-9de0-e9f6b2b09a8a" class="">Make sure there will be no security leaks</p><p id="2342881f-86f1-8011-bb90-c8bf3d9391b8" class="">Private credentials must be protected</p></div></details></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>